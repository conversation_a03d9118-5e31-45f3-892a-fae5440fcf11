import type { LoginResponseDto, UserProfileDto } from '@/types/api';
import type { ResultResponse } from '@/types/common';

import api from '../api';

const API_PATH = '/auth';

export const authApi = {
  /**
   * 用户登录
   */
  login: async (
    emailOrUsername: string,
    password: string,
  ): Promise<ResultResponse<LoginResponseDto>> => {
    return api.post(
      `${API_PATH}/login`,
      { email: emailOrUsername, password: password },
      { headers: { 'Content-Type': 'application/json', Authorization: undefined } },
    );
  },

  /**
   * 获取用户信息
   */
  getUserInfo: async (): Promise<ResultResponse<UserProfileDto>> => {
    return api.get(`${API_PATH}/profile`);
  },
};
