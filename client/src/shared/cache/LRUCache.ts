// 定义简单的LRU缓存类
export class LRUCache<K, V> {
  private readonly capacity: number;
  private cache: Map<K, V>;

  constructor(capacity: number) {
    this.capacity = capacity;
    this.cache = new Map<K, V>();
  }

  remove(key: K): void {
    this.cache.delete(key);
  }

  get(key: K): V | undefined {
    if (!this.cache.has(key)) return undefined;

    // 获取值
    const value = this.cache.get(key);

    // 删除后重新添加到最后，模拟最近使用
    this.cache.delete(key);
    if (value !== undefined) {
      this.cache.set(key, value);
    }

    return value;
  }

  put(key: K, value: V): void {
    // 如果已存在，先删除
    if (this.cache.has(key)) {
      this.cache.delete(key);
    }
    // 如果缓存已满，删除最早的项（Map的第一个元素）
    else if (this.cache.size >= this.capacity) {
      const firstKey = this.cache.keys().next().value;
      if (firstKey !== undefined) {
        this.cache.delete(firstKey);
      }
    }

    // 添加新项
    this.cache.set(key, value);
  }

  clear(): void {
    this.cache.clear();
  }
}
