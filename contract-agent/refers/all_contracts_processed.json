{"contracts": [{"metadata": {"filename": "【20210524更新】全定制服务合同模板（不涉及datlas）(1).md", "template_name": "技术服务合同", "template_type": "technical_service", "version": "v1.0"}, "header": {"contract_number": "\\[ \\]**", "signing_date": "【】年【】月**", "parties": {"party_a": {"name": "【 】(\"甲方\")**", "address": "", "contact": ""}, "party_b": {"name": "上海脉策数据科技有限公司", "address": "上海市杨浦区政立路421号C座10层", "contact": "021-61070586"}}}, "sections": [{"title": "技术服务内容与服务时间", "content": "> [运维服务期为【一年(365天)】，自开发成果验收合格之日起计算。]", "purpose": "明确服务范围、交付内容和服务期限", "filling_guide": "1. 功能名称填写具体的服务或产品模块名\n2. 功能描述简明扼要说明该功能的作用\n3. 运维服务期根据项目需求调整，通常为1-3年", "placeholders": ["一年(365天)"]}, {"title": "技术服务费用及支付方式", "content": "", "purpose": "约定服务费用总额、支付方式、付款节点和银行账户信息", "filling_guide": "1. 总金额需要同时填写数字和大写\n2. 付款期数和比例可根据项目调整，但需确保总和为100%\n3. 付款工作日一般为7-15个工作日\n4. 甲方需提供完整的开票信息", "placeholders": []}, {"title": "成果交付、验收及双方权利义务", "content": "# \n\n\n| 序号 | 里程碑事件 | 支付金额 |\n\n| 第一期 | 本合同签订后 | 技术服务费的30% |\n| | | |\n| | | 即人民币【】元(大写：【】万元整) |\n\n| 第二期 | 平台系统上线后 | 技术服务费的60% |\n| | | |\n| | | 即人民币【】元(大写：【】万元整) |\n\n| 第三期 | 经甲方验收合格后 | 技术服务费的10% |\n| | | |\n| | | 即人民币【】元(大写：【】万元整) |\n\n\n\n#\n\n\n\n\n\n\n\n\n\n\n\n\n\n#\n\n\n\n\n\n\n\n\n\n#", "purpose": "明确交付标准、验收流程以及双方的权利和责任", "filling_guide": "1. 验收期限通常为1-3个月\n2. 项目计划完成时间需根据项目复杂度确定\n3. 可根据项目特点增加特殊的权利义务条款", "placeholders": []}, {"title": "保密条款与知识产权", "content": "#\n\n\n\n\n\n\n\n#\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n#", "purpose": "保护双方商业秘密，明确知识产权归属", "filling_guide": "此部分通常为标准条款，一般不需要修改", "placeholders": []}, {"title": "违约责任与争议解决", "content": "", "purpose": "约定违约处理机制和争议解决方式", "filling_guide": "1. 违约金比例可协商，通常为千分之一到千分之五\n2. 争议解决方式可选择仲裁或诉讼", "placeholders": []}, {"title": "其他约定", "content": "\\<以下无正文，为签署页\\>\n\n甲方(盖章)：\n\n法定代表人或受委托人(签字)：\n\n签约时间： 年 月 日\n\n乙方(盖章)：上海脉策数据科技有限公司\n\n法定代表人或受委托人(签字)：\n\n签约时间： 年 月 日", "purpose": "补充其他必要条款", "filling_guide": "填写双方项目联系人信息，其他为标准条款", "placeholders": []}], "signature_block": "甲方(盖章)：\n\n法定代表人或受委托人(签字)：\n\n签约时间： 年 月 日\n\n乙方(盖章)：上海脉策数据科技有限公司\n\n法定代表人或受委托人(签字)：\n\n签约时间"}, {"metadata": {"filename": "【20220607更新】标准产品许可合同模板.md", "template_name": "软件产品许可协议", "template_type": "product_license", "version": "v1.0"}, "header": {"contract_number": "\\[ \\]**", "signing_date": "【】年【】月**", "parties": {"party_a": {"name": "【 】**", "address": "", "contact": ""}, "party_b": {"name": "上海脉策数据科技有限公司", "address": "上海市杨浦区政立路421号C座10层", "contact": "021-61070586"}}}, "sections": [{"title": "产品内容与许可期限", "content": "> [乙方产品的许可期限为【一年(365天)】]，自本产品完成在线交付之日起计算。乙方向合同约定或甲方指定的邮箱、手机号码发送开通本产品使用权限的邮件、短信，即视为乙方完成在线交付。", "purpose": "明确许可产品的内容、功能模块和使用期限", "filling_guide": "1. 选择需要的产品模块\n2. 许可期限通常为1年，可根据需求调整", "placeholders": ["一年(365天)"]}, {"title": "合同金额及支付方式", "content": "| 序号 | 支付节点 | 支付金额 |\n\n| 第一期 | 本合同签订后 | 合同总金额的60% |\n| | | |\n| | | 即人民币【】元(大写：【】万元整) |\n\n| 第二期 | 交付完成后 | 合同总金额的40% |\n| | | |\n| | | 即人民币【】元(大写：【】万元整) |", "purpose": "约定许可费用和支付安排", "filling_guide": "1. 许可费包含软件、数据订阅和功能模块费用\n2. 通常分两期支付：签约60%，交付40%", "placeholders": []}, {"title": "双方权利义务及联系人", "content": "", "purpose": "明确使用限制和双方责任", "filling_guide": "注意：产品账号不得分享给第三方，仅限中国大陆使用", "placeholders": []}, {"title": "保密条款与知识产权", "content": "", "purpose": "保护软件产品知识产权", "filling_guide": "标准条款，注意乙方产品知识产权归乙方所有", "placeholders": []}, {"title": "违约责任与争议解决", "content": "", "purpose": "约定违约处理和纠纷解决机制", "filling_guide": "逾期付款按0.1%/天计算违约金", "placeholders": []}, {"title": "其他约定", "content": "", "purpose": "补充条款", "filling_guide": "标准条款，一般不需修改", "placeholders": []}], "signature_block": "甲方(盖章)：\n\n法定代表人或受委托人(签字)：\n\n签约时间： 年 月 日\n\n乙方(盖章)：上海脉策数据科技有限公司\n\n法定代表人或受委托人(签字)：\n\n签约时间"}, {"metadata": {"filename": "20250208-北外滩街道数据保密协议-MDT-V1.0.md", "template_name": "数据保密协议", "template_type": "confidentiality", "version": "v1.0"}, "header": {"contract_number": "", "signing_date": "", "parties": {"party_a": {"name": "[[北外滩街道]]", "address": "", "contact": ""}, "party_b": {"name": "上海脉策数据科技有限公司", "address": "上海市杨浦区政立路421号C座10层", "contact": ""}}}, "sections": [{"title": "双方权利与义务", "content": "1、甲方保证，甲方对本协议项下提供的数据资源享有合法、完整的知识产权，且甲方拥有根据本协议向乙方提供数据资源并允许乙方使用的所有权利，不会侵犯第三方的合法权益。\n\n2、甲方在向乙方提供数据资源时，有权就使用、传递、保管、销毁等保密管理情况提供建议和指导，乙方应尽商业合理努力予以配合。\n\n3、乙方应当根据业务需要，以合理的方式使用甲方提供的数据资源，包括但不限于下载、数据清洗、分析、打印、存储等。未经甲方许可，乙方不得对外发布或向第三方提供数据资源，亦不得将数据资源用于合作项目以外的工作或以商业目的将数据资源用于开发和生产其他产品。为避免疑问，乙方有权在以下情形中披露数据资料：法律法规规定的应予披露的情形、任何有管辖权法院要求披露数据资料、乙方的股票上市或挂牌的任何证券交易所的规则和规定所要求披露数据资料、任何政府机构、官方机构或监管机构依法有权要求披露数据资料\n(但前提条件是，在作出相关披露前，披露信息资料的一方应将有关要求通知另一方，并应就信息披露的时间和内容咨询另一方的意见)。\n\n4、乙方应当在本单位内部针对甲方提供的数据资源采取相应的保密措施，避免与本项目实施无关的乙方工作人员接触前述数据资源，严防数据泄露。\n\n5、乙方应定期开展数据使用、传递、保管、销毁等方面的保密自查，防止发生泄密事件。同时接受并积极配合甲方对所使用的数据进行保密检查，对发现的问题，及时采取措施进行整改。", "purpose": "约定数据使用和保密责任", "filling_guide": "注意数据仅能用于合作项目，不得商业化", "placeholders": []}, {"title": "数据的归还与销毁", "content": "合作项目终止后，若收到甲方书面请求，乙方应立即归还或销毁其持有的一切涉及数据资源的原始资料，以及一切与本协议数据有关的复印件、记录、摘要或其他文件。", "purpose": "约定项目结束后的数据处理方式", "filling_guide": "标准条款，一般不需修改", "placeholders": []}, {"title": "应急处理", "content": "双方应协商确定数据资源泄密事件应急处理预案。任何一方行为导致数据资源泄露的，无论故意与过失，应当在第一时间采取一切必要措施防止数据资源的扩散，并尽最大可能消除影响。", "purpose": "约定泄密事件的处理流程", "filling_guide": "标准条款，一般不需修改", "placeholders": []}, {"title": "其他约定", "content": "1\\.\n本协议受中国法律管辖并据其解释。双方不可撤销地同意，凡因本协议引起的或与本协议有关的任何争议，均应向上海市静安区人民法院诉讼。\n\n2\\. 本保密协议书于双方签字盖章之日起生效。\n\n3\\. 本保密协议书未尽事宜按国家有关法律法规执行。\n\n4\\. 本保密协议书一式二份，甲乙双方各执一份。\n\n（以下无正文）\n\n ------------------------------------------------- ------------------------------------------------------\n [甲方（盖章）：[北外滩街道]] [乙方（盖章）：上海脉策数据科技有限公司]\n\n 签订日期： 签订日期：\n ------------------------------------------------- ------------------------------------------------------", "purpose": "法律适用和争议解决", "filling_guide": "注意管辖法院的选择", "placeholders": []}], "signature_block": "甲方（盖章）：[北外滩街道]] [乙方（盖章）：上海脉策数据科技有限公司]\n签订日期"}], "total": 3, "types": ["product_license", "confidentiality", "technical_service"]}