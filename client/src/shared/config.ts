const config: Record<string, any> = (window as any)['__ai_video_analysis_config__'] || {};

// 获取API_URL
export const API_URL = config.apiUrl || 'http://localhost:3001';
// 获取部署路径
export const DEPLOY_PATH = config.deployPath || '/';

export const AI_PREFERENCE_SETTINGS: Record<string, string> = {
  audioTranscriptModel: config.audioTranscriptModel || 'paraformer', // 音频转写模型
  textAnalysisModel: config.textAnalysisModel || 'qwen-max', // 文本分析模型
  imageAnalysisModel: config.imageAnalysisModel || 'vllm/Qwen2.5-14B-Instruct', // 图像分析模型
  videoAnalysisModel: config.videoAnalysisModel || 'vllm/Qwen2.5-14B-Instruct', // 视频分析模型
  audioProcessMode: config.audioProcessMode || '2', // 音频处理模式
};

// 是否显示AI偏好设置, 调试阶段为true
export const VISIBLE_AI_PREFERENCE_SETTINGS = config.visibleAiPreferenceSettings ?? true;
// 是否显示提示词模版, 调试阶段为true
export const VISIBLE_PROMPT_TEMPLATES = config.visiblePromptTemplates ?? true;
// 是否使用多线程FFmpeg, 默认使用多线程FFmpeg
export const USE_MT_FFMPEG = config.useMtFfmpeg ?? true;

/**
 * Paraformer模型（从配置文件可见是主要使用的ASR模型）通常要求16kHz采样率
 * 大多数主流ASR模型都是基于16kHz采样率训练的
 * 过高或过低的采样率都可能导致识别效果下降
 */
export const AUDIO_PROCESS_MODES = Array.isArray(config.audioProcessModes)
  ? config.audioProcessModes
  : [
      {
        key: '1',
        name: '方案1: 高保真无处理',
        description: '保持原始音频特征，适合高质量录音环境',
        overrideFfmpegArgs: false,
        ffmpegArgs: [
          '-q:a',
          '0', // 最高音频质量
          '-ar',
          '16000',
        ],
      },
      {
        key: '2',
        name: '方案2: 高级降噪',
        description: '高级降噪处理，适合有叠加说话人的场景',
        overrideFfmpegArgs: false,
        ffmpegArgs: [
          '-q:a',
          '1', // 较高音频质量
          // '-af',
          // 'afftdn=nf=-22:nt=w',
          '-ar',
          '16000',
          '-cpu-used',
          '1', // 性能/质量平衡
        ],
      },
      {
        key: '3',
        name: '方案3: 专业多阶段处理',
        description: '专业多阶段处理，适合背景噪音复杂的场景',
        overrideFfmpegArgs: false,
        ffmpegArgs: [
          '-q:a',
          '1', // 高音频质量
          '-af',
          // 先降噪，然后进行动态范围压缩，最后移除静音
          'afftdn=nf=-20:nt=w,dynaudnorm=p=0.95:m=15:s=10,silenceremove=1:0:-50dB',
          '-ar',
          '16000', // 语音识别优化采样率
          '-ac',
          '2', // 保持立体声
          '-cpu-used',
          '1', // 性能/质量平衡
        ],
      },
      {
        key: '4',
        name: '方案4: 语音增强+降噪',
        description: '语音增强+降噪，增强说话人声音特征',
        overrideFfmpegArgs: false,
        ffmpegArgs: [
          '-q:a',
          '1', // 高音频质量
          '-af',
          // 语音增强参数组合，先高通滤波去除低频噪音，然后平衡化处理，最后轻微降噪
          'highpass=f=100,equalizer=f=1000:width_type=o:width=2:g=3,afftdn=nf=-15:nt=w',
          '-ar',
          '16000', // 较高采样率
          '-cpu-used',
          '1', // 性能/质量平衡
        ],
      },
      {
        key: '5',
        name: '方案5: 人声频谱优化',
        description: '专注于人声频段的处理，适合多人交谈场景',
        overrideFfmpegArgs: false,
        ffmpegArgs: [
          '-q:a',
          '1', // 高音频质量
          '-af',
          // 多段均衡器增强人声频段，加轻微压缩
          'equalizer=f=200:width_type=h:width=100:g=-3,equalizer=f=3000:width_type=h:width=1000:g=3,equalizer=f=300:width_type=h:width=100:g=2,acompressor=threshold=0.05:ratio=2:attack=200:release=1000',
          '-ar',
          '16000', // 高质量采样率
          '-cpu-used',
          '1', // 性能/质量平衡
        ],
      },
      {
        key: '6',
        name: '方案6: 专业语音识别',
        description: '专业语音识别，适合需要精准语音识别的场景',
        overrideFfmpegArgs: false,
        ffmpegArgs: [
          '-q:a',
          '2', // 较高音频质量
          '-af',
          // 人声频率范围优化，去除非人声频率
          'bandpass=f=1500:width_q=1.5,loudnorm=I=-16:TP=-1.5:LRA=8',
          '-ar',
          '16000', // 语音识别标准采样率
          '-ac',
          '1', // 单声道，通常对语音识别更友好
          '-cpu-used',
          '1', // 性能/质量平衡
        ],
      },
      {
        key: '7',
        name: '方案7: 远距离录音优化',
        description: '远距离录音场景优化，增强远处人声',
        overrideFfmpegArgs: false,
        ffmpegArgs: [
          '-q:a',
          '1',
          '-af',
          // 增强远距离人声的处理链
          'highpass=f=80,lowpass=f=12000,compand=0.3|0.3:1|1:-90/-60|-60/-40|-40/-30|-20/-20:6:0:-90:0.2,equalizer=f=1000:width_type=o:width=2:g=4,afftdn=nf=-20:nt=w',
          '-ar',
          '16000',
          '-cpu-used',
          '1',
        ],
      },
      {
        key: '8',
        name: '方案8: 室外环境优化',
        description: '室外录音场景优化，抑制风噪和环境噪声',
        overrideFfmpegArgs: false,
        ffmpegArgs: [
          '-q:a',
          '1',
          '-af',
          // 室外环境优化处理链
          'highpass=f=100,lowpass=f=10000,anlmdn=s=5:p=0.001:r=0.001,dewind,equalizer=f=200:width_type=h:width=100:g=-6,equalizer=f=2000:width_type=h:width=1000:g=3,acompressor=threshold=0.1:ratio=3:attack=100:release=2000',
          '-ar',
          '16000',
          '-ac',
          '1',
          '-cpu-used',
          '1',
        ],
      },
    ];
