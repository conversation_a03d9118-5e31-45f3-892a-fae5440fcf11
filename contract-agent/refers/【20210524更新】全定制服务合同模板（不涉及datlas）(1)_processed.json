{"metadata": {"filename": "【20210524更新】全定制服务合同模板（不涉及datlas）(1).md", "template_name": "技术服务合同", "template_type": "technical_service", "version": "v1.0"}, "header": {"contract_number": "\\[ \\]**", "signing_date": "【】年【】月**", "parties": {"party_a": {"name": "【 】(\"甲方\")**", "address": "", "contact": ""}, "party_b": {"name": "上海脉策数据科技有限公司", "address": "上海市杨浦区政立路421号C座10层", "contact": "021-61070586"}}}, "sections": [{"title": "技术服务内容与服务时间", "content": "> [运维服务期为【一年(365天)】，自开发成果验收合格之日起计算。]", "purpose": "明确服务范围、交付内容和服务期限", "filling_guide": "1. 功能名称填写具体的服务或产品模块名\n2. 功能描述简明扼要说明该功能的作用\n3. 运维服务期根据项目需求调整，通常为1-3年", "placeholders": ["一年(365天)"]}, {"title": "技术服务费用及支付方式", "content": "", "purpose": "约定服务费用总额、支付方式、付款节点和银行账户信息", "filling_guide": "1. 总金额需要同时填写数字和大写\n2. 付款期数和比例可根据项目调整，但需确保总和为100%\n3. 付款工作日一般为7-15个工作日\n4. 甲方需提供完整的开票信息", "placeholders": []}, {"title": "成果交付、验收及双方权利义务", "content": "# \n\n\n| 序号 | 里程碑事件 | 支付金额 |\n\n| 第一期 | 本合同签订后 | 技术服务费的30% |\n| | | |\n| | | 即人民币【】元(大写：【】万元整) |\n\n| 第二期 | 平台系统上线后 | 技术服务费的60% |\n| | | |\n| | | 即人民币【】元(大写：【】万元整) |\n\n| 第三期 | 经甲方验收合格后 | 技术服务费的10% |\n| | | |\n| | | 即人民币【】元(大写：【】万元整) |\n\n\n\n#\n\n\n\n\n\n\n\n\n\n\n\n\n\n#\n\n\n\n\n\n\n\n\n\n#", "purpose": "明确交付标准、验收流程以及双方的权利和责任", "filling_guide": "1. 验收期限通常为1-3个月\n2. 项目计划完成时间需根据项目复杂度确定\n3. 可根据项目特点增加特殊的权利义务条款", "placeholders": []}, {"title": "保密条款与知识产权", "content": "#\n\n\n\n\n\n\n\n#\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n#", "purpose": "保护双方商业秘密，明确知识产权归属", "filling_guide": "此部分通常为标准条款，一般不需要修改", "placeholders": []}, {"title": "违约责任与争议解决", "content": "", "purpose": "约定违约处理机制和争议解决方式", "filling_guide": "1. 违约金比例可协商，通常为千分之一到千分之五\n2. 争议解决方式可选择仲裁或诉讼", "placeholders": []}, {"title": "其他约定", "content": "\\<以下无正文，为签署页\\>\n\n甲方(盖章)：\n\n法定代表人或受委托人(签字)：\n\n签约时间： 年 月 日\n\n乙方(盖章)：上海脉策数据科技有限公司\n\n法定代表人或受委托人(签字)：\n\n签约时间： 年 月 日", "purpose": "补充其他必要条款", "filling_guide": "填写双方项目联系人信息，其他为标准条款", "placeholders": []}], "signature_block": "甲方(盖章)：\n\n法定代表人或受委托人(签字)：\n\n签约时间： 年 月 日\n\n乙方(盖章)：上海脉策数据科技有限公司\n\n法定代表人或受委托人(签字)：\n\n签约时间"}