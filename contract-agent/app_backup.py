import os
import json
import uuid
import subprocess
import logging
import requests
import re
from flask import Flask, request, jsonify, send_file, send_from_directory

app = Flask(__name__)

# --- 配置日志 ---
logging.basicConfig(level=logging.DEBUG,
                    format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# --- 全局变量存储加载的数据 ---
CONTRACT_TEMPLATES = []
PROMPTS = {}

GENERATED_CONTRACTS_DIR = 'generated_contracts'
REFERS_DIR = 'refers' # 假设 refers 文件夹与 app.py 同级

# --- LLM API 配置 ---
LLM_API_URL = "http://192.168.10.119:8080/v1/chat/completions"
LLM_API_HEADERS = {
    "x-model": "llm-reasoning-default",
    "Content-Type": "application/json"
}
LLM_REQUEST_TIMEOUT = 120 # LLM请求超时时间（秒）

# --- 数据加载 ---
def load_data():
    """应用启动时加载 JSON 文件到内存"""
    global CONTRACT_TEMPLATES, PROMPTS
    
    # 加载合同模板 - 使用新的all_contracts_processed.json
    all_contracts_file = os.path.join(REFERS_DIR, 'all_contracts_processed.json')
    if os.path.exists(all_contracts_file):
        try:
            with open(all_contracts_file, 'r', encoding='utf-8') as f:
                all_contracts_data = json.load(f)
                # 提取contracts数组中的每个合同作为模板
                contracts_list = all_contracts_data.get('contracts', [])
                loaded_templates = []
                
                for contract in contracts_list:
                    # 为每个合同添加template_filename字段，基于原文件名
                    original_filename = contract.get('metadata', {}).get('filename', '')
                    if original_filename:
                        # 生成template_filename，去掉.md后缀，添加-processed.json
                        template_filename = original_filename.replace('.md', '_processed.json')
                        contract['template_filename'] = template_filename
                    
                    # 添加其他必要字段以兼容现有逻辑
                    contract['contract_type'] = contract.get('metadata', {}).get('template_name', '')
                    contract['template_note'] = contract.get('metadata', {}).get('template_name', '')
                    
                    loaded_templates.append(contract)
                
                CONTRACT_TEMPLATES = loaded_templates
                logger.info(f"成功从 '{all_contracts_file}' 加载 {len(CONTRACT_TEMPLATES)} 个合同模板。")
        except FileNotFoundError:
            logger.error(f"合同模板文件 '{all_contracts_file}' 未找到。")
            CONTRACT_TEMPLATES = []
        except json.JSONDecodeError:
            logger.error(f"合同模板文件 '{all_contracts_file}' 格式错误。")
            CONTRACT_TEMPLATES = []
    else:
        logger.error(f"合同模板文件 '{all_contracts_file}' 不存在。")
        CONTRACT_TEMPLATES = []

    # 加载prompts
    try:
        with open('prompts.json', 'r', encoding='utf-8') as f:
            PROMPTS = json.load(f)
        logger.info(f"成功加载 {len(PROMPTS)} 条 Prompts。")
    except FileNotFoundError:
        logger.error("'prompts.json' 未找到。请确保文件存在。")
        PROMPTS = {}
    except json.JSONDecodeError:
        logger.error("'prompts.json' 格式错误。")
        PROMPTS = {}

    if not os.path.exists(GENERATED_CONTRACTS_DIR):
        os.makedirs(GENERATED_CONTRACTS_DIR)
        logger.info(f"创建文件夹: {GENERATED_CONTRACTS_DIR}")

# 在应用启动前加载数据
load_data()

# --- LLM 调用 ---
def get_llm_response(prompt_name: str, user_input: str, context_data: dict = None) -> dict:
    """
    调用大模型API获取响应。
    """
    logger.info(f"LLM调用 - Prompt: {prompt_name}")
    if not PROMPTS:
        logger.error("Prompts 未加载，无法进行 LLM 调用。")
        return {"error": "Prompts未加载"}

    prompt_config = PROMPTS.get(prompt_name)
    if not prompt_config:
        logger.error(f"未找到名为 '{prompt_name}' 的Prompt配置。")
        return {"error": f"Prompt '{prompt_name}' 未配置"}

    system_message = prompt_config.get("system_message", "")
    user_prompt_template = prompt_config.get("user_prompt_template", "")

    placeholder_values = {}
    if prompt_name == "template_selection":
        placeholder_values = {
            "__USER_INPUT__": user_input,
            "__TEMPLATES_SUMMARY_JSON__": context_data.get("templates_summary_json", "[]")
        }
    elif prompt_name == "content_processing": # 修改以适应批处理
        placeholder_values = {
            "__USER_OVERALL_INPUT__": user_input, 
            "__CURRENT_TEXT_BLOCK_JSON__": context_data.get("current_text_block_json", "{}") # 键名修改
        }
    else:
        logger.warning(f"未为 prompt_name '{prompt_name}' 配置特定的 placeholder_values 构建逻辑。")
        placeholder_values = {
            "__USER_INPUT__": user_input,
            "__USER_OVERALL_INPUT__": user_input
        }
        if context_data:
            placeholder_values.update(context_data)

    try:
        final_user_prompt = user_prompt_template
        for key, value in placeholder_values.items():
            if key in final_user_prompt:
                 final_user_prompt = final_user_prompt.replace(key, str(value))
        
    except Exception as e:
        logger.error(f"填充Prompt模板 '{prompt_name}' 时发生未知错误: {e}")
        return {"error": f"填充Prompt模板时发生未知错误: {e}"}

    messages = [
        {"role": "system", "content": system_message},
        {"role": "user", "content": final_user_prompt}
    ]
    request_body = {"messages": messages, "stream": False}

    logger.info(f"向LLM API发送请求: URL={LLM_API_URL}, Prompt='{prompt_name}'")
    # logger.debug(f"LLM Request Body for {prompt_name}: {json.dumps(request_body, ensure_ascii=False, indent=2)}")

    try:
        response = requests.post(LLM_API_URL, headers=LLM_API_HEADERS, json=request_body, timeout=LLM_REQUEST_TIMEOUT)
        response.raise_for_status()
        response_json = response.json()
        logger.info(f"LLM API响应成功接收, Prompt='{prompt_name}'")

        if not response_json.get("choices") or not isinstance(response_json["choices"], list) or len(response_json["choices"]) == 0:
            logger.error("LLM响应格式错误: 'choices' 数组为空或不存在。")
            return {"error": "LLM响应格式错误: choices为空"}
        
        message_content = response_json["choices"][0].get("message", {}).get("content")
        if message_content is None:
            logger.error("LLM响应格式错误: 未找到 'content'。")
            return {"error": "LLM响应格式错误: content未找到"}

        # logger.debug(f"LLM Raw Message Content for {prompt_name}: {message_content}")

        if prompt_name == "template_selection":
            return {"selected_template_filename": message_content.strip()}
        elif prompt_name == "content_processing":
            # 对于批处理，期望LLM返回一个JSON字符串，该字符串解析后是一个字典
            # 例如：{"path.to.text1": "new text1", "path.to.text2": "[nochange]"}
            try:
                # 尝试去除可能的markdown代码块标记
                cleaned_content = message_content.strip()
                if cleaned_content.startswith("```json"):
                    cleaned_content = cleaned_content[7:]
                if cleaned_content.startswith("```"): # 有些模型可能只用 ```
                    cleaned_content = cleaned_content[3:]
                if cleaned_content.endswith("```"):
                    cleaned_content = cleaned_content[:-3]
                
                processed_block_data = json.loads(cleaned_content)
                if not isinstance(processed_block_data, dict):
                    logger.error(f"LLM content_processing响应不是预期的字典格式: {type(processed_block_data)}. Content: {cleaned_content[:200]}")
                    return {"error": "LLM content_processing响应不是字典"}
                return {"processed_block": processed_block_data} # 返回解析后的字典
            except json.JSONDecodeError as e:
                logger.error(f"解析LLM content_processing响应时发生JSONDecodeError: {e}. LLM响应文本: {message_content[:500]}")
                return {"error": f"解析LLM content_processing响应失败: {e}"}
        else:
            logger.warning(f"未为 prompt_name '{prompt_name}' 配置特定的响应处理逻辑。返回原始content。")
            return {"llm_raw_content": message_content}

    except requests.exceptions.Timeout:
        logger.error(f"LLM API请求超时 ({LLM_REQUEST_TIMEOUT}秒)。")
        return {"error": "LLM API请求超时"}
    except requests.exceptions.HTTPError as e:
        logger.error(f"LLM API HTTP错误: {e} - 响应体: {e.response.text if e.response else 'N/A'}")
        return {"error": f"LLM API HTTP错误: {e.response.status_code if e.response else 'N/A'}"}
    except requests.exceptions.RequestException as e:
        logger.error(f"LLM API请求失败: {e}")
        return {"error": f"LLM API请求失败: {e}"}
    except json.JSONDecodeError as e: 
        logger.error(f"解析LLM API原始响应时发生JSONDecodeError: {e}. 响应文本: {response.text if 'response' in locals() else 'N/A'}")
        return {"error": "解析LLM API原始响应失败"}
    except Exception as e:
        logger.error(f"处理LLM响应时发生未知错误: {e}")
        return {"error": f"处理LLM响应时发生未知错误: {e}"}

# --- Markdown 转 DOCX ---
def markdown_to_docx(markdown_content: str, contract_name: str) -> str | None:
    """使用pandoc将Markdown字符串转换为DOCX文件。返回文件路径或None。"""
    try:
        file_id = uuid.uuid4()
        md_filename = os.path.join(GENERATED_CONTRACTS_DIR, f"{contract_name}_{file_id}.md")
        docx_filename = os.path.join(GENERATED_CONTRACTS_DIR, f"{contract_name}_{file_id}.docx")

        with open(md_filename, 'w', encoding='utf-8') as f:
            f.write(markdown_content)
        
        logger.info(f"尝试将 {md_filename} 转换为 {docx_filename} 使用 pandoc")
        # pandoc input.md -o output.docx
        result = subprocess.run(['pandoc', md_filename, '-o', docx_filename],
                                capture_output=True, text=True, check=False)
        
        if result.returncode == 0:
            logger.info(f"Pandoc转换成功: {docx_filename}")
            return docx_filename
        else:
            logger.error(f"Pandoc转换失败。返回码: {result.returncode}")
            logger.error(f"Pandoc stdout: {result.stdout}")
            logger.error(f"Pandoc stderr: {result.stderr}")
            return None
    except FileNotFoundError:
        logger.error("Pandoc未找到。请确保pandoc已安装并在系统PATH中。")
        return None
    except Exception as e:
        logger.error(f"Markdown转DOCX时发生未知错误: {e}")
        return None
    finally:
        # 可选：完成后删除临时的md文件
        if os.path.exists(md_filename):
            try:
                os.remove(md_filename)
            except Exception as e:
                logger.warning(f"无法删除临时md文件 {md_filename}: {e}")

# --- 新的辅助函数：收集需要处理的文本片段 ---
def _collect_text_fragments_by_group_recursive(node: any, current_path: list, groups_data: dict, current_group_id: str = None):
    """递归辅助函数，用于收集需要LLM处理的文本片段。"""
    if isinstance(node, dict):
        for key, value in node.items():
            new_path = current_path + [key]
            _collect_text_fragments_by_group_recursive(value, new_path, groups_data, current_group_id)

    elif isinstance(node, list):
        for i, item in enumerate(node):
            new_path = current_path + [i]
            _collect_text_fragments_by_group_recursive(item, new_path, groups_data, current_group_id)

    elif isinstance(node, str) and node.strip(): # 只处理非空字符串
        if _is_value_needing_processing(current_path, node):
            # 根据路径确定逻辑分组
            group_id = _determine_group_id_from_path(current_path)
            if group_id:
                # 将路径转换为字符串key，因为JSON对象的键必须是字符串
                path_key = ".".join(map(str, current_path))
                if group_id not in groups_data:
                    groups_data[group_id] = []
                groups_data[group_id].append({
                    "path": current_path, # 保留原始路径列表用于后续更新
                    "path_key": path_key, # 字符串路径用作LLM输入/输出的键
                    "original_text": node
                })

def _determine_group_id_from_path(path: list) -> str:
    """根据路径确定应该属于哪个逻辑分组"""
    if not path:
        return None
    
    # 头部信息分组
    if path[0] == "header":
        return "metadata_block"
    
    # 章节内容分组
    if path[0] == "sections":
        if len(path) > 2:
            section_index = path[1] if isinstance(path[1], int) else 0
            section_key = path[2] if len(path) > 2 else ""
            
            # 根据章节标题判断分组
            # 这里需要根据实际的章节标题来分组，暂时简化处理
            if "费用" in str(section_key) or "支付" in str(section_key) or "金额" in str(section_key):
                return "payment_block"
            elif "保密" in str(section_key) or "知识产权" in str(section_key) or "违约" in str(section_key) or "争议" in str(section_key):
                return "standard_clauses_block"
            else:
                return "main_content_block"
    
    # 签名块
    if path[0] == "signature_block":
        return "signature_block"
    
    # 默认主内容块
    return "main_content_block"

def _collect_text_fragments_by_group(template_data: dict) -> dict:
    """收集模板中所有需要LLM处理的文本片段，按逻辑分组。"""
    groups_data = {} # 例如: {"main_content_block": [{"path": ["sections",0,"content"], "path_key": "sections.0.content", "original_text": "..."}]}
    _collect_text_fragments_by_group_recursive(template_data, [], groups_data)
    logger.debug(f"Collected text fragments by group: {json.dumps({k: len(v) for k, v in groups_data.items()}, indent=2)}")
    return groups_data

# --- 核心合同生成逻辑 ---

def _is_value_needing_processing(key_path: list, value: any) -> bool:
    """根据键路径和值判断是否需要LLM处理"""
    if not isinstance(value, str) or not value.strip(): # 只处理非空字符串
        return False

    # 不处理元数据字段
    if len(key_path) > 0 and key_path[0] == "metadata":
        return False

    # header下的特定字段需要处理（甲方信息等）
    if len(key_path) > 0 and key_path[0] == "header":
        last_key = key_path[-1]
        # 处理甲方相关信息，但不处理乙方固定信息
        if len(key_path) >= 3 and key_path[1] == "parties" and key_path[2] == "party_a":
            return True
        # 处理合同编号和签订时间
        if last_key in ["contract_number", "signing_date"]:
            return True
        return False

    # sections下的内容需要处理
    if len(key_path) > 0 and key_path[0] == "sections":
        last_key = key_path[-1]
        # 不处理元数据字段
        if last_key in ["purpose", "filling_guide", "placeholders"]:
            return False
        # 处理章节标题和内容
        if last_key in ["title", "content"]:
            return True
        return False

    # signature_block需要处理
    if len(key_path) > 0 and key_path[0] == "signature_block":
        return True

    # 包含占位符的通常需要处理
    if "【" in value or "[ ]" in value or "__" in value:
        return True

    # 默认不处理，以防意外修改了我们不希望LLM碰的结构性字符串或ID等
    logger.debug(f"Value for key path {key_path} with value '{value[:30]}...' WILL NOT be processed by LLM by default.")
    return False

# --- Markdown 组装辅助函数 ---
def _build_markdown_from_json(data: dict) -> str:
    markdown_parts = []

    # 1. 处理合同标题（从metadata获取）
    metadata = data.get("metadata", {})
    template_name = metadata.get("template_name", "合同")
    markdown_parts.append(f"# {template_name}\n\n")

    # 2. 处理头部信息（合同编号、签订时间、甲乙方信息）
    header = data.get("header", {})
    
    # 合同编号和签订时间
    if header.get("contract_number"):
        markdown_parts.append(f"**合同编号：{header['contract_number']}**\n\n")
    if header.get("signing_date"):
        markdown_parts.append(f"**签订时间：{header['signing_date']}**\n\n")
    
    # 甲乙方信息
    parties = header.get("parties", {})
    party_a = parties.get("party_a", {})
    party_b = parties.get("party_b", {})
    
    if party_a.get("name"):
        markdown_parts.append(f"**甲方：{party_a['name']}**\n")
    if party_a.get("address"):
        markdown_parts.append(f"联系地址：{party_a['address']}\n")
    if party_a.get("contact"):
        markdown_parts.append(f"联系方式：{party_a['contact']}\n\n")
    
    if party_b.get("name"):
        markdown_parts.append(f"**乙方：{party_b['name']}**\n")
    if party_b.get("address"):
        markdown_parts.append(f"联系地址：{party_b['address']}\n")
    if party_b.get("contact"):
        markdown_parts.append(f"联系方式：{party_b['contact']}\n\n")
    
    markdown_parts.append("甲方和乙方在本合同中单独称为"一方"，合称"双方"。\n\n")

    # 3. 处理章节内容
    sections = data.get("sections", [])
    for i, section in enumerate(sections):
        if not isinstance(section, dict):
            continue
            
        section_title = section.get("title", f"第{i+1}条")
        section_content = section.get("content", "")
        
        markdown_parts.append(f"## {i+1}. {section_title}\n\n")
        
        if section_content.strip():
            # 处理内容中的表格格式
            if "|" in section_content and "---" in section_content:
                # 已经是表格格式，直接使用
                markdown_parts.append(f"{section_content}\n\n")
            else:
                # 普通文本内容
                markdown_parts.append(f"{section_content}\n\n")
        else:
            # 如果没有内容，添加占位符
            markdown_parts.append("*[此章节内容待完善]*\n\n")

    # 4. 处理签名区
    signature_block = data.get("signature_block", "")
    if signature_block:
        markdown_parts.append("## 签署区\n\n")
        markdown_parts.append(f"{signature_block}\n\n")

    return "".join(markdown_parts)

def generate_contract_content(user_input: str) -> tuple[str | None, str | None, list[str] | None, str | None]: # 返回值增加 logs
    """
    根据用户输入生成合同内容。
    返回 (generated_markdown_content, selected_template_filename, accumulated_logs, error_message)
    """
    logs_accumulator = [] # 用于收集处理过程中的日志信息给前端

    # 1. 选择模板
    templates_summary = []
    for t in CONTRACT_TEMPLATES:
        summary = {
            "template_filename": t.get("template_filename"),
            "contract_type": t.get("contract_type"),
            "template_note": t.get("template_note"),
            "contract_title": t.get("metadata", {}).get("contract_title")
        }
        templates_summary.append(summary)
    
    logs_accumulator.append("步骤1: 正在选择合适的合同模板...")
    logger.debug(f"Templates summary for LLM: {json.dumps(templates_summary, ensure_ascii=False, indent=2)}")

    llm_response_selection = get_llm_response(
        prompt_name="template_selection",
        user_input=user_input,
        context_data={"templates_summary_json": json.dumps(templates_summary, ensure_ascii=False)}
    )

    if "error" in llm_response_selection or "selected_template_filename" not in llm_response_selection:
        error_msg = f"模板选择失败: {llm_response_selection.get('error', '未知错误')}"
        logs_accumulator.append(f"错误: {error_msg}")
        logger.error(error_msg)
        return None, None, logs_accumulator, error_msg
    
    selected_template_filename = llm_response_selection["selected_template_filename"]
    logs_accumulator.append(f"步骤1: 成功选择模板 '{selected_template_filename}'")
    logger.info(f"LLM选定模板文件名 '{selected_template_filename}'")

    selected_template_data = next((t for t in CONTRACT_TEMPLATES if t.get("template_filename") == selected_template_filename), None)
    
    if not selected_template_data:
        error_msg = f"未在已加载的CONTRACT_TEMPLATES中找到文件名为 '{selected_template_filename}' 的模板数据。"
        logs_accumulator.append(f"错误: {error_msg}")
        logger.error(error_msg)
        return None, selected_template_filename, logs_accumulator, error_msg
    
    logger.info(f"成功匹配到模板对象: {selected_template_data.get('template_note', selected_template_filename)}")

    import copy
    modifiable_template_data = copy.deepcopy(selected_template_data)

    # 2. 按llm_group_id收集所有需要处理的文本片段
    logs_accumulator.append("步骤2: 正在从模板中收集各逻辑块的文本内容...")
    grouped_text_fragments = _collect_text_fragments_by_group(modifiable_template_data)
    
    if not grouped_text_fragments:
        logs_accumulator.append("警告: 未在模板中找到任何可供LLM处理的文本片段组。将使用原始模板。")
        logger.warning("未在模板中找到任何按llm_group_id分组的可处理文本片段。")
    else:
        logs_accumulator.append(f"步骤2: 成功收集到 {len(grouped_text_fragments)} 个文本逻辑块。")

        # 定义处理顺序
        processing_order = ["metadata_block", "main_content_block", "payment_block", "standard_clauses_block"]
        # 也可以从 grouped_text_fragments.keys() 获取所有实际存在的组，并按预定义顺序排序

        for group_id in processing_order:
            if group_id not in grouped_text_fragments or not grouped_text_fragments[group_id]:
                logger.info(f"跳过空的或不存在的文本组: {group_id}")
                logs_accumulator.append(f"处理 {group_id}: 该块无内容或无需处理。")
                continue

            current_block_fragments = grouped_text_fragments[group_id]
            # 构建给LLM的JSON对象: {"path.key1": "text1", "path.key2": "text2", ...}
            block_to_llm = {item["path_key"]: item["original_text"] for item in current_block_fragments}
            
            log_msg_start = f"步骤2.{processing_order.index(group_id)+1}: 开始处理逻辑块 '{group_id}' ({len(block_to_llm)} 项文本)..."
            logs_accumulator.append(log_msg_start)
            logger.info(log_msg_start)

            llm_response_block = get_llm_response(
                prompt_name="content_processing",
                user_input=user_input, # 用户整体需求
                context_data={"current_text_block_json": json.dumps(block_to_llm, ensure_ascii=False)}
            )

            if "error" in llm_response_block or "processed_block" not in llm_response_block:
                error_msg_block = f"处理逻辑块 '{group_id}' 时LLM出错: {llm_response_block.get('error', '未知错误')}. 该块内容将保持不变。"
                logs_accumulator.append(f"错误: {error_msg_block}")
                logger.error(error_msg_block)
                continue # 继续处理下一个块
            
            processed_results = llm_response_block["processed_block"] # 这是个字典 {"path.key": "new_text_or_nochange"}
            changes_count = 0
            no_changes_count = 0

            # 根据LLM返回的结果更新 modifiable_template_data
            for fragment_info in current_block_fragments:
                original_path = fragment_info["path"]      # list of keys/indices
                path_key_str = fragment_info["path_key"]   # "key1.0.key2"

                if path_key_str in processed_results:
                    llm_output_for_fragment = processed_results[path_key_str]
                    if isinstance(llm_output_for_fragment, str) and llm_output_for_fragment.strip().lower() == "[nochange]":
                        no_changes_count +=1
                    else:
                        # 更新 modifiable_template_data 中的值
                        current_level = modifiable_template_data
                        for i, path_segment in enumerate(original_path):
                            if i == len(original_path) - 1: # 到达最后一个路径段，设置新值
                                if isinstance(current_level, dict) and path_segment in current_level:
                                    current_level[path_segment] = llm_output_for_fragment
                                    changes_count += 1
                                elif isinstance(current_level, list) and isinstance(path_segment, int) and path_segment < len(current_level):
                                    current_level[path_segment] = llm_output_for_fragment
                                    changes_count += 1
                                else:
                                    logger.warning(f"无法在路径 {original_path} 为逻辑块 '{group_id}' 设置值 '{llm_output_for_fragment[:30]}...'. 当前层级类型: {type(current_level)}")
                                    logs_accumulator.append(f"警告: 更新路径 {path_key_str} 失败。")
                            else: # 继续深入
                                if isinstance(current_level, dict) and path_segment in current_level:
                                    current_level = current_level[path_segment]
                                elif isinstance(current_level, list) and isinstance(path_segment, int) and path_segment < len(current_level):
                                    current_level = current_level[path_segment]
                                else:
                                    logger.warning(f"路径 {original_path} 在深入到 {path_segment} 时中断。")
                                    logs_accumulator.append(f"警告: 路径 {path_key_str} 无效。")
                                    break # 跳出内部循环，此片段更新失败
                else:
                    logger.warning(f"LLM响应中未包含路径键 '{path_key_str}' (来自逻辑块 '{group_id}')。该片段将保持不变。")
                    logs_accumulator.append(f"警告: LLM未返回路径 {path_key_str} 的结果。")
            
            log_msg_end = f"步骤2.{processing_order.index(group_id)+1}: 逻辑块 '{group_id}' 处理完成。修改了 {changes_count} 项，{no_changes_count} 项无变化。"
            logs_accumulator.append(log_msg_end)
            logger.info(log_msg_end)

    # 3. 从处理后的JSON组装最终Markdown
    logs_accumulator.append("步骤3: 正在从更新后的模板数据生成最终Markdown文档...")
    logger.info(f"开始从处理后的JSON数据组装Markdown...")
    final_markdown_content = _build_markdown_from_json(modifiable_template_data) # 使用更新后的数据
    logs_accumulator.append("步骤3: Markdown文档生成成功。")
    logger.info(f"INFO: 最终合同Markdown组装完成。")
    # --- DEBUGGING ---
    app.logger.debug("---------- DEBUG: Final Processed Template Data START ----------")
    processed_data_str = json.dumps(modifiable_template_data, ensure_ascii=False, indent=2)
    app.logger.debug(processed_data_str)
    app.logger.debug("---------- DEBUG: Final Processed Template Data END ----------")
    # --- END DEBUGGING ---
    
    return final_markdown_content, selected_template_filename, logs_accumulator, None

# --- API 端点 ---
@app.route('/chat', methods=['POST'])
def chat_handler():
    data = request.json
    user_input = data.get('user_input')

    if not user_input:
        return jsonify({"error": "请求体中未提供 'user_input'"}), 400
    
    logger.info(f"收到 /chat 请求, user_input: {user_input[:100]}...")

    markdown_content, template_name, logs, error = generate_contract_content(user_input) # 接收logs

    if error:
        return jsonify({"error": error, "selected_template_name": template_name, "logs": logs}), 500 # 返回logs
    
    if not markdown_content:
        return jsonify({"error": "未能生成合同内容", "selected_template_name": template_name, "logs": logs}), 500 # 返回logs

    return jsonify({
        "message": "合同已生成", 
        "selected_template_name": template_name,
        "contract_markdown": markdown_content,
        "logs": logs # 返回logs
    }), 200

@app.route('/download_contract', methods=['POST'])
def download_handler():
    data = request.json
    markdown_content = data.get('markdown_content')
    contract_name = data.get('contract_name', 'GeneratedContract') # 客户端可以指定名称

    if not markdown_content:
        return jsonify({"error": "请求体中未提供 'markdown_content'"}), 400
    
    logger.info(f"收到 /download_contract 请求, 合同名称: {contract_name}")

    docx_filepath = markdown_to_docx(markdown_content, contract_name)

    if not docx_filepath:
        return jsonify({"error": "Markdown转DOCX失败"}), 500
    
    try:
        return send_file(docx_filepath, as_attachment=True, download_name=os.path.basename(docx_filepath))
    except Exception as e:
        logger.error(f"发送文件时出错: {e}")
        return jsonify({"error": "发送文件失败"}), 500
    # finally:
        # 可选：发送后删除服务器上的docx文件
        # if os.path.exists(docx_filepath):
        #     try:
        #         os.remove(docx_filepath)
        #     except Exception as e:
        #        logger.warning(f"无法删除已发送的docx文件 {docx_filepath}: {e}")

# --- 新增前端服务路由 ---
@app.route('/')
def serve_index():
    return send_from_directory('static', 'index.html')

@app.route('/static/<path:filename>') # 如果index.html引用了其他css/js文件，也需要路由
def serve_static(filename):
    return send_from_directory('static', filename)

if __name__ == '__main__':
    app.run(debug=True, port=5001) 