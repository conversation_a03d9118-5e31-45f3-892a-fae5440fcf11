import re
import json
from typing import Dict, List, Any

class ContractProcessor:
    """合同文档处理器"""
    
    def __init__(self):
        # 需要清理的格式标记
        self.format_marks = [
            r'\{\.mark\}',
            r'\{\.underline\}', 
            r'\{\.paragraph-insertion[^}]+\}',
            r'\[\]{\.underline}',
            r'#+\s*\{#[^}]+\}',
        ]
        
        # 技术服务合同的章节合并规则
        self.tech_service_merge_rules = {
            "技术服务内容与服务时间": ["技术服务内容", "服务内容", "运维服务期"],
            "技术服务费用及支付方式": ["技术服务费用", "支付方式", "银行", "开户", "开票"],
            "成果交付、验收及双方权利义务": ["成果交付", "验收", "权利义务", "联系人职责"],
            "保密条款与知识产权": ["陈述与保证", "保密", "知识产权"],
            "违约责任与争议解决": ["违约责任", "不可抗力", "合同解除", "争议解决"],
            "其他约定": ["通知与送达", "其他约定", "签署及生效"]
        }
        
        # 产品许可合同的章节合并规则  
        self.product_license_merge_rules = {
            "产品内容与许可期限": ["产品内容", "许可期限"],
            "合同金额及支付方式": ["合同金额", "支付方式", "银行", "开户", "开票"],
            "双方权利义务及联系人": ["双方权利义务", "联系人职责"],
            "保密条款与知识产权": ["陈述与保证", "保密", "知识产权"],
            "违约责任与争议解决": ["违约责任", "不可抗力", "合同解除", "争议解决"],
            "其他约定": ["通知与送达", "其他约定"]
        }
        
        # 保密协议的章节合并规则
        self.confidentiality_merge_rules = {
            "数据资源说明": ["数据名称", "数据内容"],
            "双方权利与义务": ["双方权利与义务"],
            "数据的归还与销毁": ["数据的归还与销毁"],
            "应急处理": ["应急处理"],
            "其他约定": ["其他"]
        }
    
    def clean_markdown(self, content: str) -> str:
        """清理Markdown格式"""
        # 清理所有格式标记
        for pattern in self.format_marks:
            content = re.sub(pattern, '', content)
        
        # 清理多余的空白
        content = re.sub(r'\n{3,}', '\n\n', content)
        content = re.sub(r' {2,}', ' ', content)
        
        # 清理表格分隔线
        content = re.sub(r'\+[-\+]+\+', '', content)  # 移除 +---+ 格式的表格线
        content = re.sub(r'\|(\s*-+\s*\|)+', '|---|', content)  # 简化表格分隔线
        
        return content.strip()
    
    def identify_contract_type(self, content: str) -> str:
        """识别合同类型"""
        if '技术服务合同' in content:
            return 'technical_service'
        elif '软件产品许可协议' in content:
            return 'product_license'
        elif '数据保密协议' in content:
            return 'confidentiality'
        else:
            return 'unknown'
    
    def extract_sections_for_tech_service(self, content: str) -> List[Dict[str, Any]]:
        """提取技术服务合同的章节"""
        return self._extract_and_merge_sections(content, self.tech_service_merge_rules)
    
    def extract_sections_for_product_license(self, content: str) -> List[Dict[str, Any]]:
        """提取产品许可合同的章节"""
        return self._extract_and_merge_sections(content, self.product_license_merge_rules)
    
    def extract_sections_for_confidentiality(self, content: str) -> List[Dict[str, Any]]:
        """提取保密协议的章节"""
        return self._extract_and_merge_sections(content, self.confidentiality_merge_rules)
    
    def _extract_and_merge_sections(self, content: str, merge_rules: Dict) -> List[Dict[str, Any]]:
        """通用的章节提取和合并方法"""
        lines = content.split('\n')
        raw_sections = []
        current_section = None
        section_content = []
        
        for line in lines:
            # 识别章节标题（# 开头或数字编号开头）
            if (line.strip().startswith('# ') and not line.strip().startswith('##')) or \
               re.match(r'^\d+\.\s+\S', line.strip()) or \
               re.match(r'^[一二三四五六七八九十]+、', line.strip()) or \
               re.match(r'^\*\*[一二三四五六七八九十]+、', line.strip()):
                
                if current_section:
                    current_section['content'] = '\n'.join(section_content).strip()
                    raw_sections.append(current_section)
                
                title = re.sub(r'^#+\s*', '', line.strip())
                title = re.sub(r'^\d+\.\s*', '', title)
                title = re.sub(r'^[一二三四五六七八九十]+、', '', title)
                title = re.sub(r'^\*\*([一二三四五六七八九十]+、.*?)\*\*', r'\1', title)
                title = re.sub(r'^[一二三四五六七八九十]+、', '', title)
                
                current_section = {
                    'title': title.strip(),
                    'content': ''
                }
                section_content = []
            elif current_section:
                section_content.append(line)
        
        if current_section:
            current_section['content'] = '\n'.join(section_content).strip()
            raw_sections.append(current_section)
        
        # 合并章节
        merged_sections = []
        used_indices = set()
        
        for merged_title, keywords in merge_rules.items():
            merged_content = []
            
            for i, section in enumerate(raw_sections):
                if i in used_indices:
                    continue
                    
                for keyword in keywords:
                    if keyword in section['title'] or \
                       (len(section['content']) > 50 and keyword in section['content'][:200]):
                        merged_content.append(section['content'])
                        used_indices.add(i)
                        break
            
            if merged_content:
                merged_sections.append({
                    'title': merged_title,
                    'content': '\n\n'.join(merged_content).strip()
                })
        
        return merged_sections
    
    def extract_placeholders(self, content: str) -> List[str]:
        """提取占位符"""
        placeholders = set()
        matches = re.findall(r'【([^】]+)】', content)
        
        for match in matches:
            # 过滤掉过长的内容和备注
            if len(match) < 20 and not match.startswith('备注') and not match.startswith('请'):
                placeholders.add(match)
        
        return sorted(list(placeholders))
    
    def add_metadata_tech_service(self, section: Dict) -> Dict:
        """为技术服务合同章节添加元数据"""
        metadata = {
            "技术服务内容与服务时间": {
                "purpose": "明确服务范围、交付内容和服务期限",
                "filling_guide": "1. 功能名称填写具体的服务或产品模块名\n2. 功能描述简明扼要说明该功能的作用\n3. 运维服务期根据项目需求调整，通常为1-3年"
            },
            "技术服务费用及支付方式": {
                "purpose": "约定服务费用总额、支付方式、付款节点和银行账户信息",
                "filling_guide": "1. 总金额需要同时填写数字和大写\n2. 付款期数和比例可根据项目调整，但需确保总和为100%\n3. 付款工作日一般为7-15个工作日\n4. 甲方需提供完整的开票信息"
            },
            "成果交付、验收及双方权利义务": {
                "purpose": "明确交付标准、验收流程以及双方的权利和责任",
                "filling_guide": "1. 验收期限通常为1-3个月\n2. 项目计划完成时间需根据项目复杂度确定\n3. 可根据项目特点增加特殊的权利义务条款"
            },
            "保密条款与知识产权": {
                "purpose": "保护双方商业秘密，明确知识产权归属",
                "filling_guide": "此部分通常为标准条款，一般不需要修改"
            },
            "违约责任与争议解决": {
                "purpose": "约定违约处理机制和争议解决方式",
                "filling_guide": "1. 违约金比例可协商，通常为千分之一到千分之五\n2. 争议解决方式可选择仲裁或诉讼"
            },
            "其他约定": {
                "purpose": "补充其他必要条款",
                "filling_guide": "填写双方项目联系人信息，其他为标准条款"
            }
        }
        
        if section['title'] in metadata:
            section['purpose'] = metadata[section['title']]['purpose']
            section['filling_guide'] = metadata[section['title']]['filling_guide']
        
        section['placeholders'] = self.extract_placeholders(section['content'])
        return section
    
    def add_metadata_product_license(self, section: Dict) -> Dict:
        """为产品许可合同章节添加元数据"""
        metadata = {
            "产品内容与许可期限": {
                "purpose": "明确许可产品的内容、功能模块和使用期限",
                "filling_guide": "1. 选择需要的产品模块\n2. 许可期限通常为1年，可根据需求调整"
            },
            "合同金额及支付方式": {
                "purpose": "约定许可费用和支付安排",
                "filling_guide": "1. 许可费包含软件、数据订阅和功能模块费用\n2. 通常分两期支付：签约60%，交付40%"
            },
            "双方权利义务及联系人": {
                "purpose": "明确使用限制和双方责任",
                "filling_guide": "注意：产品账号不得分享给第三方，仅限中国大陆使用"
            },
            "保密条款与知识产权": {
                "purpose": "保护软件产品知识产权",
                "filling_guide": "标准条款，注意乙方产品知识产权归乙方所有"
            },
            "违约责任与争议解决": {
                "purpose": "约定违约处理和纠纷解决机制",
                "filling_guide": "逾期付款按0.1%/天计算违约金"
            },
            "其他约定": {
                "purpose": "补充条款",
                "filling_guide": "标准条款，一般不需修改"
            }
        }
        
        if section['title'] in metadata:
            section['purpose'] = metadata[section['title']]['purpose']
            section['filling_guide'] = metadata[section['title']]['filling_guide']
        
        section['placeholders'] = self.extract_placeholders(section['content'])
        return section
    
    def add_metadata_confidentiality(self, section: Dict) -> Dict:
        """为保密协议章节添加元数据"""
        metadata = {
            "数据资源说明": {
                "purpose": "明确保密数据的名称和内容范围",
                "filling_guide": "详细描述需要保密的数据资源"
            },
            "双方权利与义务": {
                "purpose": "约定数据使用和保密责任",
                "filling_guide": "注意数据仅能用于合作项目，不得商业化"
            },
            "数据的归还与销毁": {
                "purpose": "约定项目结束后的数据处理方式",
                "filling_guide": "标准条款，一般不需修改"
            },
            "应急处理": {
                "purpose": "约定泄密事件的处理流程",
                "filling_guide": "标准条款，一般不需修改"
            },
            "其他约定": {
                "purpose": "法律适用和争议解决",
                "filling_guide": "注意管辖法院的选择"
            }
        }
        
        if section['title'] in metadata:
            section['purpose'] = metadata[section['title']]['purpose']
            section['filling_guide'] = metadata[section['title']]['filling_guide']
        
        section['placeholders'] = self.extract_placeholders(section['content'])
        return section
    
    def extract_header(self, content: str, contract_type: str) -> Dict[str, Any]:
        """提取合同头部信息"""
        header = {
            'contract_number': '',
            'signing_date': '',
            'parties': {
                'party_a': {
                    'name': '',
                    'address': '',
                    'contact': ''
                },
                'party_b': {
                    'name': '上海脉策数据科技有限公司',
                    'address': '',
                    'contact': ''
                }
            }
        }
        
        # 提取合同编号
        contract_num_match = re.search(r'合同编号[：:]\s*([^\n]+)', content)
        if contract_num_match:
            header['contract_number'] = contract_num_match.group(1).strip()
        
        # 提取签订时间
        date_match = re.search(r'签订时间[：:]\s*([^\n]+)', content)
        if date_match:
            header['signing_date'] = date_match.group(1).strip()
        
        # 提取甲方信息
        party_a_match = re.search(r'甲方[:：]\s*([^\n]+)', content)
        if party_a_match:
            header['parties']['party_a']['name'] = party_a_match.group(1).strip()
        
        # 提取乙方地址
        if '杨浦区政立路421号' in content:
            header['parties']['party_b']['address'] = '上海市杨浦区政立路421号C座10层'
        elif '杨浦区政立路421号10层' in content:
            header['parties']['party_b']['address'] = '上海市杨浦区政立路421号10层'
            
        # 提取联系方式
        if '021-61070586' in content:
            header['parties']['party_b']['contact'] = '021-61070586'
        
        return header
    
    def extract_signature_block(self, content: str) -> str:
        """提取签名块"""
        # 查找签名区域的不同模式
        patterns = [
            r'甲方\s*[\(（]盖章[\)）][：:］][\s\S]*?乙方\s*[\(（]盖章[\)）][：:］][\s\S]*?签[订约]?[时日]间',
            r'甲方（盖章）[\s\S]*?乙方（盖章）[\s\S]*?签订日期',
            r'\n\s*甲方[\s\S]*?法定代表人[\s\S]*?乙方[\s\S]*?法定代表人'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                return self.clean_signature_block(match.group(0))
        
        # 默认签名块
        return "甲方(盖章)：\n法定代表人或受委托人(签字)：\n签约时间： 年 月 日\n\n乙方(盖章)：上海脉策数据科技有限公司\n法定代表人或受委托人(签字)：\n签约时间： 年 月 日"
    
    def clean_signature_block(self, signature: str) -> str:
        """清理签名块格式"""
        # 移除多余的空白和下划线
        signature = re.sub(r'_{2,}', '', signature)
        signature = re.sub(r'-{2,}', '', signature)
        signature = re.sub(r'\s{3,}', '\n', signature)
        return signature.strip()
    
    def process_contract(self, content: str, filename: str) -> Dict[str, Any]:
        """处理单个合同"""
        # 清理内容
        cleaned_content = self.clean_markdown(content)
        
        # 识别合同类型
        contract_type = self.identify_contract_type(content)
        
        # 根据合同类型提取章节
        if contract_type == 'technical_service':
            sections = self.extract_sections_for_tech_service(cleaned_content)
            add_metadata = self.add_metadata_tech_service
            template_name = '技术服务合同'
        elif contract_type == 'product_license':
            sections = self.extract_sections_for_product_license(cleaned_content)
            add_metadata = self.add_metadata_product_license
            template_name = '软件产品许可协议'
        elif contract_type == 'confidentiality':
            sections = self.extract_sections_for_confidentiality(cleaned_content)
            add_metadata = self.add_metadata_confidentiality
            template_name = '数据保密协议'
        else:
            sections = []
            add_metadata = lambda x: x
            template_name = '未知合同类型'
        
        # 为每个章节添加元数据
        for section in sections:
            add_metadata(section)
        
        # 构建结果
        result = {
            'metadata': {
                'filename': filename,
                'template_name': template_name,
                'template_type': contract_type,
                'version': 'v1.0'
            },
            'header': self.extract_header(cleaned_content, contract_type),
            'sections': sections,
            'signature_block': self.extract_signature_block(cleaned_content)
        }
        
        return result


# 处理三个文件的函数
def process_all_contracts():
    processor = ContractProcessor()
    
    # 三个合同文件的路径
    contract_files = [
        {
            'filename': '【20210524更新】全定制服务合同模板（不涉及datlas）(1).md',
            'filepath': '【20210524更新】全定制服务合同模板（不涉及datlas）(1).md'
        },
        {
            'filename': '【20220607更新】标准产品许可合同模板.md', 
            'filepath': '【20220607更新】标准产品许可合同模板.md'
        },
        {
            'filename': '20250208-北外滩街道数据保密协议-MDT-V1.0.md',
            'filepath': '20250208-北外滩街道数据保密协议-MDT-V1.0.md'
        }
    ]
    
    results = []
    
    for contract_info in contract_files:
        print(f"处理文件: {contract_info['filename']}")
        
        # 读取文件内容
        try:
            with open(contract_info['filepath'], 'r', encoding='utf-8') as f:
                content = f.read()
        except FileNotFoundError:
            print(f"  - 错误：找不到文件 {contract_info['filepath']}")
            continue
        except Exception as e:
            print(f"  - 错误：读取文件时出错 {e}")
            continue
        
        result = processor.process_contract(content, contract_info['filename'])
        results.append(result)
        
        # 保存单个文件
        output_name = contract_info['filename'].replace('.md', '_processed.json')
        with open(output_name, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        print(f"  - 合同类型: {result['metadata']['template_name']}")
        print(f"  - 章节数量: {len(result['sections'])}")
        print(f"  - 保存至: {output_name}\n")
    
    # 保存所有合同到一个文件
    all_contracts = {
        'contracts': results,
        'total': len(results),
        'types': list(set(r['metadata']['template_type'] for r in results))
    }
    
    with open('all_contracts_processed.json', 'w', encoding='utf-8') as f:
        json.dump(all_contracts, f, ensure_ascii=False, indent=2)
    
    print("所有合同处理完成！")
    print(f"共处理了 {len(results)} 个合同文件")
    print("生成的文件:")
    for contract_info in contract_files:
        output_name = contract_info['filename'].replace('.md', '_processed.json')
        print(f"  - {output_name}")
    print("  - all_contracts_processed.json")
    
    return results


if __name__ == "__main__":
    # 脚本会自动读取当前目录下的三个md文件并处理
    results = process_all_contracts()
