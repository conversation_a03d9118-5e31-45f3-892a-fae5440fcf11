[{"template_name": "20250208-北外滩街道数据保密协议-MDT-V1.0", "description": "用于北外滩街道与其他方进行数据合作时，规定双方数据保密责任的协议。", "keywords": ["数据保密", "北外滩街道", "数据合作", "保密协议"], "contract_type": "保密协议", "file_name": "20250208-北外滩街道数据保密协议-MDT-V1.0.md", "core_clauses_identifiers": ["一、双方权利与义务", "二、数据的归还与销毁", "三、应急处理"], "fixed_elements": {"contract_number_placeholder_text": "编号：\\[ \\]", "party_a_name_placeholder_text": "甲方： [[北外滩街道]{.mark}]{.underline}", "party_b_name_placeholder_text": "乙方： [上海脉策数据科技有限公司]{.underline}", "signing_date_placeholder_text": "日期： [2025年2月]{.underline}", "party_a_full_name_placeholder_text": "[[【北外滩街道】]{.mark}]{.underline}", "party_a_address_placeholder_text": "地址：[【 】]{.underline}", "party_a_contact_person_placeholder_text": "联系人：[【 】]{.underline}", "party_a_contact_method_placeholder_text": "联系方式：[【 】]{.underline}", "party_b_full_name_placeholder_text": "[上海脉策数据科技有限公司]{.underline}", "party_b_address_placeholder_text": "地址：[【上海市杨浦区政立路421号C座10层 】]{.underline}", "party_b_contact_person_placeholder_text": "联系人：[【 】]{.underline}", "party_b_contact_method_placeholder_text": "联系方式：[【 】]{.underline}", "project_name_placeholder_text": "【 [城市治理垂类语料研究\\n】合作]{.underline}项目", "data_name_placeholder_text": "[[【政务服务中的相关问答资料】]{.mark}]{.underline}", "data_content_description_placeholder_text": "[以甲方实际交付给乙方的数据资源为准]{.underline}", "data_type_confirmation_placeholder_text": "[【政务服务中的相关问答资料】]{.mark}"}, "clauses": [{"clause_id": "intro_data_description", "clause_title": "引言和数据描述", "original_content_md": "为保障【 [城市治理垂类语料研究\\n】合作]{.underline}项目的顺利实施，甲方拟向乙方提供本协议项下的数据资源(\"数据资源\")。双方就业务协同过程中所涉及的数据保密责任达成如下数据保密协议，以资双方共同遵守：\\n\\n1.数据名称： [[【政务服务中的相关问答资料】]{.mark}]{.underline}\\n\\n数据内容： [以甲方实际交付给乙方的数据资源为准]{.underline}\\n\\n甲方确认，数据资源为[【政务服务中的相关问答资料】]{.mark}，不涉及个人信息(个人信息是指以电子或者其他方式记录的能够单独或者与其他信息结合识别特定自然人的各种信息，包括自然人的姓名、出生日期、身份证件号码、生物识别信息、住址、电话号码、电子邮箱、健康信息、行踪信息等）。为避免异议，数据资源不包括以下数据或信息：(a)非因乙方披露而为或成为公众所知的数据或信息，(b)在甲方提供以前，乙方已经正当获取的数据或信息，(c)乙方从第三方处合法获取的数据或信息，且不违反任何保密限制或保密义务，以及(d)由乙方独立开发而未使用任何数据资源或者违反接受在本协议项下任何义务的数据或信息。", "placeholders_in_clause": [], "formatting_guidelines": {"heading_level": null, "contains_bullet_list": false, "preserve_bold_text": true}, "is_core_clause": false}, {"clause_id": "rights_obligations", "clause_title": "一、双方权利与义务", "original_content_md": "**一、双方权利与义务**\\n\\n1、甲方保证，甲方对本协议项下提供的数据资源享有合法、完整的知识产权，且甲方拥有根据本协议向乙方提供数据资源并允许乙方使用的所有权利，不会侵犯第三方的合法权益。\\n\\n2、甲方在向乙方提供数据资源时，有权就使用、传递、保管、销毁等保密管理情况提供建议和指导，乙方应尽商业合理努力予以配合。\\n\\n3、乙方应当根据业务需要，以合理的方式使用甲方提供的数据资源，包括但不限于下载、数据清洗、分析、打印、存储等。未经甲方许可，乙方不得对外发布或向第三方提供数据资源，亦不得将数据资源用于合作项目以外的工作或以商业目的将数据资源用于开发和生产其他产品。为避免疑问，乙方有权在以下情形中披露数据资料：法律法规规定的应予披露的情形、任何有管辖权法院要求披露数据资料、乙方的股票上市或挂牌的任何证券交易所的规则和规定所要求披露数据资料\\n(但前提条件是，在作出相关披露前，披露信息资料的一方应将有关要求通知另一方，并应就信息披露的时间和内容咨询另一方的意见)。\\n\\n4、乙方应当在本单位内部针对甲方提供的数据资源采取相应的保密措施，避免与本项目实施无关的乙方工作人员接触前述数据资源，严防数据泄露。\\n\\n5、乙方应定期开展数据使用、传递、保管、销毁等方面的保密自查，防止发生泄密事件。同时接受并积极配合甲方对所使用的数据进行保密检查，对发现的问题，及时采取措施进行整改。", "placeholders_in_clause": [], "formatting_guidelines": {"heading_level": 1, "is_bold_title": true, "numbered_list": true}, "is_core_clause": true}, {"clause_id": "return_destruction", "clause_title": "二、数据的归还与销毁", "original_content_md": "**二、数据的归还与销毁**\\n\\n合作项目终止后，若收到甲方书面请求，乙方应立即归还或销毁其持有的一切涉及数据资源的原始资料，以及一切与本协议数据有关的复印件、记录、摘要或其他文件。", "placeholders_in_clause": [], "formatting_guidelines": {"heading_level": 1, "is_bold_title": true}, "is_core_clause": true}, {"clause_id": "emergency_handling", "clause_title": "三、应急处理", "original_content_md": "**三、应急处理**\\n\\n双方应协商确定数据资源泄密事件应急处理预案。任何一方行为导致数据资源泄露的，无论故意与过失，应当在第一时间采取一切必要措施防止数据资源的扩散，并尽最大可能消除影响。", "placeholders_in_clause": [], "formatting_guidelines": {"heading_level": 1, "is_bold_title": true}, "is_core_clause": true}, {"clause_id": "others", "clause_title": "四、其他", "original_content_md": "**四、其他**\\n\\n1\\.\\n本协议受中国法律管辖并据其解释。双方不可撤销地同意，凡因本协议引起的或与本协议有关的任何争议，均应向上海市静安区人民法院诉讼。\\n\\n2\\. 本保密协议书于双方签字盖章之日起生效。\\n\\n3\\. 本保密协议书未尽事宜按国家有关法律法规执行。\\n\\n4\\. 本保密协议书一式二份，甲乙双方各执一份。", "placeholders_in_clause": [], "formatting_guidelines": {"heading_level": 1, "is_bold_title": true, "numbered_list": true}, "is_core_clause": false}, {"clause_id": "signatures", "clause_title": "签署部分", "original_content_md": "（以下无正文）\\n\\n  ------------------------------------------------- ------------------------------------------------------\\n  [甲方（盖章）：[北外滩街道]{.mark}]{.underline}   [乙方（盖章）：上海脉策数据科技有限公司]{.underline}\\n\\n  签订日期：                                        签订日期：\\n  ------------------------------------------------- ------------------------------------------------------", "placeholders_in_clause": [{"name": "甲方盖章处", "description": "甲方公司盖章区域", "placeholder_text": "[甲方（盖章）：[北外滩街道]{.mark}]{.underline}", "original_text_example_in_md": "[甲方（盖章）：[北外滩街道]{.mark}]{.underline}"}, {"name": "乙方盖章处", "description": "乙方公司盖章区域", "placeholder_text": "[乙方（盖章）：上海脉策数据科技有限公司]{.underline}", "original_text_example_in_md": "[乙方（盖章）：上海脉策数据科技有限公司]{.underline}"}], "formatting_guidelines": {"preserve_spacing_and_layout": true}, "is_core_clause": false}]}, {"template_name": "全定制服务合同模板（不涉及datlas）", "description": "用于甲方向乙方（上海脉策数据科技有限公司）采购全定制化技术服务的合同模板，不包含Datlas标准产品的授权。", "keywords": ["技术服务", "定制开发", "项目合同", "上海脉策"], "contract_type": "技术服务合同", "file_name": "【20210524更新】全定制服务合同模板（不涉及datlas）(1).md", "core_clauses_identifiers": ["技术服务内容与服务时间", "技术服务费用及支付方式", "成果交付及验收", "双方权利义务", "保密条款", "知识产权", "违约责任"], "fixed_elements": {"contract_number_placeholder_text": "**合同编号：\\[ \\]**", "project_title_suffix": "项目", "contract_name": "技术服务合同", "revision_mode_note": "[【请用修订模式填写】]{.mark}", "signing_year_placeholder_text": "【】年", "signing_month_placeholder_text": "【】月", "party_a_name_placeholder_text": "甲方:【 】(\"甲方\")", "party_a_address_placeholder_text": "联 系 地 址：", "party_a_contact_placeholder_text": "联 系 方 式：", "party_b_full_name": "乙方：上海脉策数据科技有限公司( \"乙方\")", "party_b_address": "联 系 地 址：上海市杨浦区政立路421号C座10层", "party_b_contact": "联 系 方 式：021-********", "project_name_in_intro_placeholder_text": "【\n[】]{.underline}平台项目", "bank_info_party_b_name": "户名：上海脉策数据科技有限公司", "bank_info_party_b_bank": "开户银行：上海浦东发展银行杨浦支行", "bank_info_party_b_account": "账号：************** 1146 3", "tax_info_party_b_name": "纳税人名称：上海脉策数据科技有限公司", "tax_info_party_b_id": "纳税人识别号：91310110342309083H", "invoice_info_party_a_name_placeholder_text": "名称： 【】", "invoice_info_party_a_tax_id_placeholder_text": "纳税人识别号：【】", "invoice_info_party_a_address_placeholder_text": "地址： 【】", "invoice_info_party_a_phone_placeholder_text": "电话： 【】", "invoice_info_party_a_bank_placeholder_text": "开户行：【】 ", "invoice_info_party_a_account_placeholder_text": "账号： 【】", "notice_party_a_address_placeholder_text": "地址：", "notice_party_a_contact_person_placeholder_text": "联系人：", "notice_party_a_phone_placeholder_text": "电话：", "notice_party_a_email_placeholder_text": "邮箱：", "notice_party_b_address": "地址：上海市杨浦区政立路421号10层", "notice_party_b_contact_person_placeholder_text": "联系人：", "notice_party_b_phone": "电话：021-********", "notice_party_b_email_placeholder_text": "邮箱："}, "clauses": [{"clause_id": "intro_agreement", "clause_title": "引言", "original_content_md": "甲方和乙方在本合同中单独称为\"**一方**\"，合称\"**双方**\"。\n\n双方根据《中华人民共和国民法典》及相关法律法规的规定，本着平等自愿的原则，经双方友好协商一致，就甲方委托乙方针对【\n[】]{.underline}平台项目(以下简称\"**本项目**\")提供技术服务(\"**服务**\")的相关事宜，达成如下合意，以兹共同遵守。", "placeholders_in_clause": [{"name": "项目名称", "description": "本次技术服务对应的项目具体名称", "placeholder_text": "【\n[】]{.underline}平台项目", "original_text_example_in_md": "【\n[】]{.underline}平台项目"}], "formatting_guidelines": {"preserve_bold_text": true}, "is_core_clause": false}, {"clause_id": "service_content_time", "clause_title": "技术服务内容与服务时间", "original_content_md": "# 技术服务内容与服务时间\n\n# 本合同提供服务包括以下内容：[【备注：请添加标准产品或服务的描述，可参考下表】]{.mark}\n\n  -------------------- --------------------------------------------------\n      **功能名称**                        **功能描述**\n\n      【客户研究】              【根据生命周期法，制作客户地图】\n\n      【板块研判】              【板块多维度价值及市场综合评估】\n\n      【决策大屏】                【快速研判的综合分析工作台】\n  -------------------- --------------------------------------------------\n\n# 运维服务期\n\n> [运维服务期为【一年(365天)】，自开发成果验收合格之日起计算。]{.mark}", "placeholders_in_clause": [{"name": "服务内容备注", "description": "服务内容表格的备注信息", "placeholder_text": "[【备注：请添加标准产品或服务的描述，可参考下表】]{.mark}", "original_text_example_in_md": "[【备注：请添加标准产品或服务的描述，可参考下表】]{.mark}"}, {"name": "功能名称1", "description": "服务内容中的第一个功能模块名称", "placeholder_text": "【客户研究】", "original_text_example_in_md": "【客户研究】"}, {"name": "功能描述1", "description": "服务内容中的第一个功能模块描述", "placeholder_text": "【根据生命周期法，制作客户地图】", "original_text_example_in_md": "【根据生命周期法，制作客户地图】"}, {"name": "功能名称2", "description": "服务内容中的第二个功能模块名称", "placeholder_text": "【板块研判】", "original_text_example_in_md": "【板块研判】"}, {"name": "功能描述2", "description": "服务内容中的第二个功能模块描述", "placeholder_text": "【板块多维度价值及市场综合评估】", "original_text_example_in_md": "【板块多维度价值及市场综合评估】"}, {"name": "功能名称3", "description": "服务内容中的第三个功能模块名称", "placeholder_text": "【决策大屏】", "original_text_example_in_md": "【决策大屏】"}, {"name": "功能描述3", "description": "服务内容中的第三个功能模块描述", "placeholder_text": "【快速研判的综合分析工作台】", "original_text_example_in_md": "【快速研判的综合分析工作台】"}, {"name": "运维服务期", "description": "运维服务的具体期限，例如：一年(365天)", "placeholder_text": "[运维服务期为【一年(365天)】，自开发成果验收合格之日起计算。]{.mark}", "original_text_example_in_md": "[运维服务期为【一年(365天)】，自开发成果验收合格之日起计算。]{.mark}"}], "formatting_guidelines": {"heading_level": 1, "contains_table": true, "table_headers": ["功能名称", "功能描述"], "blockquote_text": true, "preserve_bold_text": true}, "is_core_clause": true}, {"clause_id": "service_fee_payment", "clause_title": "技术服务费用及支付方式", "original_content_md": "# 技术服务费用及支付方式\n\n# 作为乙方向甲方提供本合同项下提供服务的对价，甲方应向乙方支付技术服务费总额人民币【】元(大写：【】万元整)(含6%的增值税，\"技术服务费\")。具体而言，本技术服务费包含以下部分：\n\n  -------------------------------------- --------------------------------\n                   服务                                价格\n\n                   【】                                【】\n\n                   【】                                【】\n\n                   【】                                【】\n  -------------------------------------- --------------------------------\n\n# 支付方式[【备注：由项目负责人根据实际情况调整】]{.mark}\n\n# 技术服务费由甲方分【三】期支付给乙方，具体支付时间和金额约定如下：\n\n# \n\n+--------+---------------------+--------------------------------------+\n| 序号   | 里程碑事件          | 支付金额                               |\n+--------+---------------------+--------------------------------------+\n| 第一期 | 本合同签订后        | 技术服务费的30%                      |\n|        |                     |                                      |\n|        |                     | 即人民币【】元(大写：【】万元整)     |\n+--------+---------------------+--------------------------------------+\n| 第二期 | 平台系统上线后      | 技术服务费的60%                      |\n|        |                     |                                      |\n|        |                     | 即人民币【】元(大写：【】万元整)     |\n+--------+---------------------+--------------------------------------+\n| 第三期 | 经甲方验收合格后    | 技术服务费的10%                      |\n|        |                     |                                      |\n|        |                     | 即人民币【】元(大写：【】万元整)     |\n+--------+---------------------+--------------------------------------+\n\n# 在每一期付款的里程碑事件达成后，乙方向甲方开具合法有效的、与实际支付金额一致的增值税专用发票。甲方在收到发票后【15】个工作日内向乙方指定的账户全额支付当期的技术服务费用。甲方理解并同意，在乙方收到本合同约定的每一期全额技术服务费之前，乙方有权拒绝继续提供服务、提供下一阶段的服务或交付下一阶段的交付成果。[【备注：由项目负责人根据实际情况调整开票和付款的顺序】]{.mark}\n\n# 支出。除双方另有规定外，前述服务费仅为乙方向甲方提供本合同项下交付成果的对价，不含乙方由于受委托提供服务所产生的如差旅费、住宿费等各项费用(\"支出\")。乙方的支出应向甲方申请核准后由甲方在服务费外另行实报实销。", "placeholders_in_clause": [{"name": "技术服务费总额-人民币数字", "description": "技术服务费总金额的人民币小写数字", "placeholder_text": "人民币【】元", "original_text_example_in_md": "人民币【】元"}, {"name": "技术服务费总额-人民币大写", "description": "技术服务费总金额的人民币大写汉字", "placeholder_text": "(大写：【】万元整)", "original_text_example_in_md": "(大写：【】万元整)"}, {"name": "服务1名称及价格", "description": "分项服务1的名称和价格", "placeholder_text": "【】                                【】", "original_text_example_in_md": "【】                                【】"}, {"name": "支付方式备注", "description": "支付方式的备注信息", "placeholder_text": "[【备注：由项目负责人根据实际情况调整】]{.mark}", "original_text_example_in_md": "[【备注：由项目负责人根据实际情况调整】]{.mark}"}, {"name": "支付期数", "description": "技术服务费分期支付的总期数", "placeholder_text": "分【三】期", "original_text_example_in_md": "分【三】期"}, {"name": "第一期支付金额-人民币数字", "description": "第一期支付金额的人民币小写数字", "placeholder_text": "即人民币【】元", "original_text_example_in_md": "即人民币【】元(大写：【】万元整)"}, {"name": "第一期支付金额-人民币大写", "description": "第一期支付金额的人民币大写汉字", "placeholder_text": "(大写：【】万元整)", "original_text_example_in_md": "即人民币【】元(大写：【】万元整)"}, {"name": "第二期支付金额-人民币数字", "description": "第二期支付金额的人民币小写数字", "placeholder_text": "即人民币【】元", "original_text_example_in_md": "即人民币【】元(大写：【】万元整)"}, {"name": "第二期支付金额-人民币大写", "description": "第二期支付金额的人民币大写汉字", "placeholder_text": "(大写：【】万元整)", "original_text_example_in_md": "即人民币【】元(大写：【】万元整)"}, {"name": "第三期支付金额-人民币数字", "description": "第三期支付金额的人民币小写数字", "placeholder_text": "即人民币【】元", "original_text_example_in_md": "即人民币【】元(大写：【】万元整)"}, {"name": "第三期支付金额-人民币大写", "description": "第三期支付金额的人民币大写汉字", "placeholder_text": "(大写：【】万元整)", "original_text_example_in_md": "即人民币【】元(大写：【】万元整)"}, {"name": "收到发票后付款工作日数", "description": "甲方收到发票后付款的工作日数", "placeholder_text": "【15】个工作日", "original_text_example_in_md": "【15】个工作日"}, {"name": "开票付款顺序备注", "description": "开票和付款顺序的备注", "placeholder_text": "[【备注：由项目负责人根据实际情况调整开票和付款的顺序】]{.mark}", "original_text_example_in_md": "[【备注：由项目负责人根据实际情况调整开票和付款的顺序】]{.mark}"}], "formatting_guidelines": {"heading_level": 1, "contains_table": true, "table_headers": ["服务", "价格", "序号", "里程碑事件", "支付金额"], "preserve_bold_text": true}, "is_core_clause": true}, {"clause_id": "bank_tax_info", "clause_title": "乙方及甲方开票信息", "original_content_md": "# 乙方开户银行名称、地址和账号为：\n\n# 户名：上海脉策数据科技有限公司\n\n# 开户银行：上海浦东发展银行杨浦支行\n\n# 账号：************** 1146 3\n\n# 纳税人名称：上海脉策数据科技有限公司\n\n# 纳税人识别号：91310110342309083H\n\n# 甲方开票信息：\n\n# 名称： 【】\n\n# 纳税人识别号：【】\n\n# 地址： 【】\n\n# 电话： 【】\n\n# 开户行：【】 \n\n# 账号： 【】", "placeholders_in_clause": [{"name": "甲方发票-公司名称", "description": "甲方开具发票时所需的公司全称", "placeholder_text": "名称： 【】", "original_text_example_in_md": "名称： 【】"}, {"name": "甲方发票-纳税人识别号", "description": "甲方开具发票时所需的纳税人识别号", "placeholder_text": "纳税人识别号：【】", "original_text_example_in_md": "纳税人识别号：【】"}, {"name": "甲方发票-地址", "description": "甲方开具发票时所需的地址", "placeholder_text": "地址： 【】", "original_text_example_in_md": "地址： 【】"}, {"name": "甲方发票-电话", "description": "甲方开具发票时所需的电话", "placeholder_text": "电话： 【】", "original_text_example_in_md": "电话： 【】"}, {"name": "甲方发票-开户行", "description": "甲方开具发票时所需的开户行名称", "placeholder_text": "开户行：【】 ", "original_text_example_in_md": "开户行：【】 "}, {"name": "甲方发票-账号", "description": "甲方开具发票时所需的银行账号", "placeholder_text": "账号： 【】", "original_text_example_in_md": "账号： 【】"}], "formatting_guidelines": {"heading_level": 1, "preserve_bold_text": false}, "is_core_clause": false}, {"clause_id": "delivery_acceptance", "clause_title": "成果交付及验收", "original_content_md": "# 成果交付及验收\n\n# 成果交付\n\n# \n\n# 甲方项目联系人应在乙方完成交付后签署相关确认文件。\n\n# \n\n# 验收范围\n\n# 本项目采用【现场验收】方式验收，以本合同第1条约定的服务为验收范围。\n\n# 验收条件\n\n# [【备注：具体验收条件由项目负责人根据项目具体情况确定】]{.mark}\n\n# 验收时间\n\n# 成果交付之日起【3】个月内(\"验收期\")，甲方应组织验收。验收合格确认单可由本合同约定的甲方项目联系人签字后，向乙方提供确认单原件或扫描件。\n\n# 甲方认为系统存在问题需要修复的，应当在验收期内通过书面形式向乙方反馈。甲方未在验收期内进行验收、未向乙方提出书面修复要求的，视为乙方交付的成果符合本合同约定，验收合格。合同约定的服务期内，乙方对平台系统承担维护义务，保证平台系统正常使用。\n\n# \n\n# 无论其他任何约定，以下情形不属于乙方原因导致的瑕疵或缺陷，乙方无需承担任何违约责任，亦无义务免费提供任何补救或维护服务：\n\n# 甲方未按照乙方提供的方法、规则、流程等进行操作，从而引起的平台系统的任何瑕疵或缺陷；\n\n# \n\n# 甲方未能满足平台系统所需环境条件或外部要求，从而引起的平台系统的任何瑕疵或缺陷；\n\n# \n\n# 甲方未经乙方同意，对平台系统进行了拆除、变更、重装、改装等行为，从而引起的平台系统的任何瑕疵或缺陷；\n\n# \n\n# 甲方将平台系统用于本合同之外目的，从而引起的平台系统的任何瑕疵或缺陷。", "placeholders_in_clause": [{"name": "验收方式", "description": "项目的验收方式，例如：现场验收", "placeholder_text": "【现场验收】", "original_text_example_in_md": "【现场验收】"}, {"name": "验收条件备注", "description": "验收条件的备注信息", "placeholder_text": "[【备注：具体验收条件由项目负责人根据项目具体情况确定】]{.mark}", "original_text_example_in_md": "[【备注：具体验收条件由项目负责人根据项目具体情况确定】]{.mark}"}, {"name": "验收期-月数", "description": "成果交付后的验收期限，单位：月", "placeholder_text": "【3】个月", "original_text_example_in_md": "【3】个月"}], "formatting_guidelines": {"heading_level": 1, "preserve_bold_text": false, "numbered_list": true}, "is_core_clause": true}, {"clause_id": "rights_obligations_parties", "clause_title": "双方权利义务", "original_content_md": "# 双方权利义务[【备注：项目负责人根据项目具体情况增减删改】]{.mark}\n\n# 甲方的权利义务\n\n# 甲方应按照合同约定及时足额地向乙方支付服务费用。\n\n# \n\n# 甲方负责提供业务需求资料。\n\n# 甲方须及时配合乙方对系统进行测试和试运行，并及时反馈修改意见给乙方。乙方可根据甲方的反馈，配置专项人员，提供必要的技术服务和培训支持，乙方对甲方提出的技术服务相关问题提供的答疑服务响应时间不超过24小时。\n\n# \n\n# 甲方保证向乙方提供的或在使用乙方产品过程中使用的任何数据或信息均不以任何方式或途径侵犯任何第三方的合法权益(包括但不限于知识产权、隐私权等)，亦不会造成任何第三方向乙方追索任何赔偿或补偿等，否则由甲方承担相应责任，并赔偿由此给乙方造成的损失。\n\n# 乙方服务仅供甲方在中国大陆范围内使用，甲方不得违反法律规定向境外（包含中国香港、澳门、台湾）传输从乙方获取的数据。\n\n# 若因甲方未能履行本条款约定的义务导致乙方提供服务延后的，乙方的交付期限应相应延长，且不视为乙方违约。\n\n# 乙方的权利义务\n\n# 乙方负责根据甲方的具体需求进行设计开发，并及时与甲方沟通，确保系统的功能符合实际操作和管理需要。\n\n# \n\n# [乙方应在收到甲方第一期付款后【】个工作日内完成项目的调研、开发、上线计划。]{.mark}\n\n# \n\n# 乙方负责根据甲方需求进行开发，提供高质量的运行系统。\n\n# \n\n# 运维服务内，乙方应确保系统性能稳定和运行正常，由于乙方技术问题带来的服务中断，乙方负责尽快恢复服务。", "placeholders_in_clause": [{"name": "双方权利义务备注", "description": "双方权利义务条款的备注", "placeholder_text": "[【备注：项目负责人根据项目具体情况增减删改】]{.mark}", "original_text_example_in_md": "[【备注：项目负责人根据项目具体情况增减删改】]{.mark}"}, {"name": "乙方收到第一期付款后项目计划完成工作日数", "description": "乙方应在收到甲方第一期付款后多少个工作日内完成项目的调研、开发、上线计划", "placeholder_text": "[乙方应在收到甲方第一期付款后【】个工作日内完成项目的调研、开发、上线计划。]{.mark}", "original_text_example_in_md": "[乙方应在收到甲方第一期付款后【】个工作日内完成项目的调研、开发、上线计划。]{.mark}"}], "formatting_guidelines": {"heading_level": 1, "preserve_bold_text": false, "numbered_list": true}, "is_core_clause": true}, {"clause_id": "project_contact_persons", "clause_title": "项目联系人职责", "original_content_md": "# 项目联系人职责\n\n1.  双方约定在本合同有效期内，甲方指定【】为甲方项目联系人，乙方指定【】为乙方项目联系人。项目联系人的联系方式以本合同第【13】条为准。\n\n# 项目联系人在本合同履行过程中承担以下职责：\n\n# 沟通协调，保障项目顺利开展；\n\n# \n\n# 项目交付验收及相关确认文件的签署；\n\n# \n\n# 甲方付款的关联公司变更情况通知与确认。\n\n# 一方变更项目联系人的，应当及时以书面形式通知另一方。未及时通知并影响本合同履行或造成损失的，应承担相应的责任。", "placeholders_in_clause": [{"name": "甲方项目联系人姓名", "description": "甲方指定的项目联系人姓名", "placeholder_text": "甲方指定【】为甲方项目联系人", "original_text_example_in_md": "甲方指定【】为甲方项目联系人"}, {"name": "乙方项目联系人姓名", "description": "乙方指定的项目联系人姓名", "placeholder_text": "乙方指定【】为乙方项目联系人", "original_text_example_in_md": "乙方指定【】为乙方项目联系人"}, {"name": "联系人条款序号", "description": "合同中约定联系人信息条款的序号", "placeholder_text": "第【13】条", "original_text_example_in_md": "第【13】条"}], "formatting_guidelines": {"heading_level": 1, "preserve_bold_text": false, "numbered_list": true}, "is_core_clause": false}, {"clause_id": "representations_warranties", "clause_title": "陈述与保证", "original_content_md": "# 陈述与保证\n\n# 双方陈述并保证：\n\n# 其系在其成立地有效存续的企业，并有完全的能力与法律资格订立和履行本合同；\n\n# \n\n# 其已经取得一切必要公司行为授权以签署和履行本合同且其签署与履行本合同不会与任何中国法律发生冲突或造成其违反对其有约束力的任何合同；\n\n# \n\n# 不存在其为当事方的可能会对其履行本合同项下义务之能力造成潜在的或实际的重大不利影响的未决诉讼、仲裁事项或其他争议；以及\n\n# \n\n# 在期限内的任何时间，如果其情况、知识或认知发生改变，致使其无法在相关时间遵守本合同的任何条款，其将立即书面通知另一方。", "placeholders_in_clause": [], "formatting_guidelines": {"heading_level": 1, "preserve_bold_text": false, "numbered_list": true}, "is_core_clause": false}, {"clause_id": "confidentiality", "clause_title": "保密条款", "original_content_md": "# 保密条款\n\n# 保密信息。一方应该将本合同签署的事实以及其内容和履行本合同中所获悉的另一方相关的秘密信息(包括但不限于一方的交付的产品、商业、技术、财务数据和/或相关资料，以及由一方提供的个人信息，以下称\"保密信息\")保密。为避免疑问，乙方的保密信息包括但不限于本合同过程中向甲方提供或甲方所获悉的平台系统、信息数据库结构及相关技术、说明文档等技术秘密和其他秘密信息。\n\n# 本合同项下的保密信息不包含下述信息：\n\n# 在获悉时已经是公开的信息；\n\n# 接受信息的当事人已经知道的信息；\n\n# 非因接受信息的当事人的责任导致公开信息的；\n\n# 接受信息的当事人从具有正当权利的第三方，合法地不承担保守秘密义务地得到的信息；\n\n# 根据法律规定或者政府机关要求而必须公开的信息。\n\n# 非授予。双方确认保密信息之提供，并不表示授予任何一方任何权利或许可，亦不表示任何一方有义务授予另一方任何相关权利或许可。\n\n# 保密义务。为保持所有保密信息的保密性，双方同意：\n\n# 未经披露保密信息的一方(\"披露方\")事先的书面同意，接收保密信息的一方(\"接收方\")不应当向任何第三方披露保密信息的任何部分；\n\n# 若一方为了履行本合同，确实需要向第三方公开保密信息，可以在需要的最低限度范围内向第三方公开秘密信息，但前提是事先通知披露方并获得披露方的事先书面同意。此时，接收方应保证所有获得保密信息的第三方均遵守本保密条款；\n\n# 当管辖政府部门或根据法律法规要求公开相关的保密信息时，双方均应立即通知另一方，在与另一方协商可能实施的信息保护对策的条件下，可以遵照法律法规的要求在必须的最低限度范围内公开保密信息。\n\n# 一经完成或终止后返还保密信息。一经服务完成或本合同提前终止，或在任何时间应披露方的要求，接收方应在该等完成、终止或披露方的要求之日起五(5)个工作日内将保密信息(包括其副本)返还给披露方。如果披露方认为返还任何保密信息是不可行的，则接收方应当销毁保密信息，并于该等完成、终止或披露方的要求之日起五(5)个工作日内书面证明该等销毁。\n\n# 本合同项下保密义务的期限为永久。本条的约定在本合同终止或期满后继续有效。", "placeholders_in_clause": [], "formatting_guidelines": {"heading_level": 1, "preserve_bold_text": false, "numbered_list": true}, "is_core_clause": true}, {"clause_id": "intellectual_property", "clause_title": "知识产权", "original_content_md": "# 知识产权\n\n# \"知识产权\"指计算机软件程序与系统、数据库权利、著作权、专利、专利申请、实用新型、商标、服务标志、注册外观设计、未注册外观设计权、精神权利、技术设计图、商号、网络域名、品牌、专有技术、发明、保密信息和其他所有工业或商业知识产权，无论位于何处、是否注册且无论是否满足注册条件，及上述各项产权的所有注册或保护申请。\n\n# 在先知识产权。在本合同签署日前，乙方拥有、开发或许可的，用于履行服务的知识产权(统称为\"在先知识产权\")仍专属于乙方所有。为避免疑问，无论其他任何约定，乙方产品及乙方在培训、实施、维护等服务过程中提供的操作手册、产品说明书等资料文档均为乙方的在先知识产权，归乙方单独所有。仅为本合同项下接受服务之目的，在许可有效期内，乙方向甲方授予一项非排他、不得转让且不得分许可的许可，甲方有权为接受服务之目的，使用相关知识产权且无需另行支付其他费用。\n\n# 甲方通过自主操作平台系统生成的图表、报告等内容为**\"平台系统生成数据\"**。除非法律另有规定，甲方对基于自己上传的原始数据而产生的平台系统生成数据享有知识产权，甲方有权内部使用或向第三方披露该等信息，但甲方不得以商业目的向第三方提供平台系统生成数据。\n\n# 未经乙方许可，甲方不得向第三方披露或提供任何乙方产品，亦不得自行或指示第三方使用技术手段对任何乙方产品进行爬虫采集或反编译、采用逆向工程或其他手段探寻乙方产品源代码及其原始架构、对乙方产品知识产权进行任何更新和/或改进。倘若甲方违反本前述约定，属于对本合同的重大违约，除本合同约定或法定的其他权利之外，乙方有权主张：(1)要求甲方和/或经甲方许可使用的任何第三方立即停止使用任何乙方产品和/或更新和/或改进，(2)要求甲方立即无偿向乙方转让任何更新和/或改进所涉及的任何及全部的知识产权；以及(3)要求甲方向乙方支付违约金，违约金的金额为本合同的【5】倍，倘若前述违约金不能弥补乙方的其他损失，甲方应当另行给予补偿。\n\n# 无许可。除非本合同另有规定或双方另有书面约定，本合同的任何内容不得被解释为乙方向甲方授予乙方当前和将来拥有的任何知识产权中的任何权利、所有权或利益。\n\n# 甲方认可并授权乙方在其简介或其他具有广告宣传性质的材料中引用甲方之名称，并允许乙方提及其为甲方的供应商。\n\n# 乙方承诺本合同所涉的乙方产品不侵犯任何第三方的合法权益。\n\n# 本条的约定在本合同终止或期满后继续有效。", "placeholders_in_clause": [{"name": "知识产权违约金倍数", "description": "知识产权条款中约定的违约金为合同金额的倍数", "placeholder_text": "【5】倍", "original_text_example_in_md": "【5】倍"}], "formatting_guidelines": {"heading_level": 1, "preserve_bold_text": true, "numbered_list": true}, "is_core_clause": true}, {"clause_id": "liability_breach", "clause_title": "违约责任", "original_content_md": "# 违约责任\n\n# 除本合同特别约定之外，任何一方违反本合同约定的，均应根据《中华人民共和国民法典》以及其他相关法律法规向守约方承担违约责任。\n\n# \"重大违约\"是指：\n\n# 一方对本合同项下义务、承诺、陈述和/或保证的重大违反，包括对其在保密条款、知识产权条款项下义务，以及对本合同其他条款项下(为本合同之目的其性质应视为重大的)义务的违反；\n\n# 一方未于发生后十五(15)个工作日或另一方书面指定的日期内以令另一方合理满意的方式纠正的重大违约之外的其他违约行为；\n\n# 一方违反适用之法律；\n\n# 一方的故意不当行为和/或重大过失。\n\n# 一方发生重大违约后，守约方可以经书面通知违约方，随时立即终止本合同，并要求违约方赔偿因该等重大违约造成的所有损失、损害、伤害和费用 (包括合理的律师费，\"损失\")。\n\n# 对于一方违反其在本合同下义务而引起的任何第三方索赔、要求、责任、诉讼、法律行动、损失、损害赔偿、费用、开支和合理的律师费，违约方应为守约方提供抗辩并使守约方免受因此引起的任何损失并对守约方进行赔偿。\n\n# 甲方理解，下述情况不属于乙方违约：\n\n# 由于网络波动造成的访问速度下降或访问中断；\n\n# 乙方在进行服务配置、系统升级维护时，需要短时间中断服务。\n\n# 在任何情况下，乙方均不对任何间接性、后果性、惩戒性、偶然性的损害，包括甲方使用本软件系统服务而遭受的利润损失承担责任。\n\n# 本合同签订后，任何一方无正当理由不得单方面终止本合同，否则视为违约。违约方应向守约方支付相当于本合同总金额20%的违约金。 \n\n# 逾期付款。甲方未在本合同约定的付款期限内按时足额向乙方支付合同款项的，自甲方应当支付之日起开始计算违约金，违约金的计算标准为逾期支付金额的0.1%乘以逾期天数。逾期超过30日的，乙方有权停止服务并解除合同，且甲方仍需承担违约损害赔偿责任。\n\n# 无论其他任何约定，乙方就本合同向甲方承担的责任以其已经收到的技术服务费金额为限的直接经济损失。", "placeholders_in_clause": [], "formatting_guidelines": {"heading_level": 1, "preserve_bold_text": false, "numbered_list": true}, "is_core_clause": true}, {"clause_id": "force_majeure", "clause_title": "不可抗力", "original_content_md": "# 不可抗力\n\n# 如发生不可抗力(指诸如流行病、大规模流行病或传染性疾病引起的突发公共卫生事件、地震、台风、洪水、火灾、爆炸、战争、网络中断或其它任何一方不可预见且超出其合理控制范围的事件，阻碍本合同任何一方履行其本合同项下的义务，\"不可抗力\")，受阻一方应毫无迟延地通知另一方，并在通知后十五(15)个工作日内提供有关该等事件的详细资料和由公证机构出具证明该等事件的公证文件，解释不能或延迟履行其在本合同项下全部或部分义务的原因。\n\n# 如果一项不可抗力或其后果阻碍一方或双方履行本合同项下的部分或全部义务达三十(30)个工作日或更长时间，则双方应根据该等不可抗力对本合同履行的影响，考虑决定是否终止本合同或免除本合同部分义务的履行或推迟本合同的履行。\n\n# 一方因不可抗力事件而不能履行或不能按时履行合同时，在不可抗力期间，提出受不可抗力影响的一方可免于承担违约责任。\n\n# 提出受不可抗力影响的一方应尽可能地采取必要的措施减轻不可抗力对合同履行造成的影响。双方应通过友好协商在合理的时间内就合同履行达成进一步协议。", "placeholders_in_clause": [], "formatting_guidelines": {"heading_level": 1, "preserve_bold_text": false, "numbered_list": true}, "is_core_clause": false}, {"clause_id": "contract_termination", "clause_title": "合同解除", "original_content_md": "# 合同解除\n\n# 除本合同另有约定，一方在下列一个或多个事件发生时，可以随时经提前三十(30)日书面通知另一方终止本合同：\n\n# 一方通过清算决议或法院发出相关清算命令，或一方提出破产申请或未能在三十(30)日内解除针对其提出的任何破产申请；\n\n# 一方资不抵债或被宣布资不抵债，或为其债权人做出任何安排或和解召开会议或提出建议，或根据破产法或类似法律做出任何其他安排；\n\n# 一方委派接管人或管理人管理其任何重大资产，或任何债权人占有或扣押一方的任何重大资产；\n\n# 一方停止或威胁停止经营业务。", "placeholders_in_clause": [], "formatting_guidelines": {"heading_level": 1, "preserve_bold_text": false, "numbered_list": true}, "is_core_clause": false}, {"clause_id": "assignment", "clause_title": "合同的转让", "original_content_md": "# 合同的转让\n\n# 未经另一方事先书面同意，任何一方不得转让其在本合同项下的任何权利或义务。", "placeholders_in_clause": [], "formatting_guidelines": {"heading_level": 1, "preserve_bold_text": false}, "is_core_clause": false}, {"clause_id": "law_dispute_resolution", "clause_title": "法律适用与争议解决", "original_content_md": "# 法律适用与争议解决\n\n# 本协议受中国法律管辖并据其解释。双方不可撤销地同意，凡因本协议引起的或与本协议有关的任何争议，均应向【】人民法院诉讼。", "placeholders_in_clause": [{"name": "争议解决法院", "description": "发生争议时选择的管辖法院名称", "placeholder_text": "【】人民法院", "original_text_example_in_md": "【】人民法院"}], "formatting_guidelines": {"heading_level": 1, "preserve_bold_text": false}, "is_core_clause": false}, {"clause_id": "notices", "clause_title": "通知", "original_content_md": "# 通知\n\n# 为履行本合同而相互发出或者提供的所有通知、文件、资料，均应以中文书面形式，按本合同首页所列明的地址专人送达、或以挂号信、或公认的快递服务或以传真方式发送到另一方。每一方的上述信息在本合同有效期内发生变更时，应当提前书面通知对方当事人。以专人交付的通知，在交付时视为送达；以预付邮资的挂号信件发出的通知，在投邮后第五(5)日视为送达；以快递服务发出的通知，在交给快递服务后第三(3)日视为送达；以传真方式发出的通知，在成功传送和收到确认时视为送达。所有通知均应发送至以下地址：\n\n**甲方**\n\n地址：\n\n联系人：\n\n电话：\n\n邮箱：\n\n**乙方**\n\n地址：上海市杨浦区政立路421号10层\n\n联系人：\n\n电话：021-********\n\n邮箱：", "placeholders_in_clause": [{"name": "甲方通知地址", "description": "甲方接收通知的地址", "placeholder_text": "地址：", "original_text_example_in_md": "地址："}, {"name": "甲方通知联系人", "description": "甲方接收通知的联系人", "placeholder_text": "联系人：", "original_text_example_in_md": "联系人："}, {"name": "甲方通知电话", "description": "甲方接收通知的电话", "placeholder_text": "电话：", "original_text_example_in_md": "电话："}, {"name": "甲方通知邮箱", "description": "甲方接收通知的邮箱", "placeholder_text": "邮箱：", "original_text_example_in_md": "邮箱："}, {"name": "乙方通知联系人", "description": "乙方接收通知的联系人", "placeholder_text": "联系人：", "original_text_example_in_md": "联系人："}, {"name": "乙方通知邮箱", "description": "乙方接收通知的邮箱", "placeholder_text": "邮箱：", "original_text_example_in_md": "邮箱："}], "formatting_guidelines": {"heading_level": 1, "preserve_bold_text": true}, "is_core_clause": false}, {"clause_id": "entire_agreement", "clause_title": "完整协议", "original_content_md": "# 完整协议\n\n# 本合同及附件构成双方之间就本合同标的的全部约定，并取代双方此前就该标的进行的所有口头或书面的协议、会议纪要、承诺、谅解备忘录、确认函及往来信件、传真。", "placeholders_in_clause": [], "formatting_guidelines": {"heading_level": 1, "preserve_bold_text": false}, "is_core_clause": false}, {"clause_id": "attachments", "clause_title": "附件", "original_content_md": "# 附件\n\n# 本合同的附件是本合同不可分割的组成部分，与本合同具有同等法律效力。", "placeholders_in_clause": [], "formatting_guidelines": {"heading_level": 1, "preserve_bold_text": false}, "is_core_clause": false}, {"clause_id": "severability_amendment", "clause_title": "可分割性与修改", "original_content_md": "# 可分割性\n\n# 如果本合同的任何条款在任何方面被有管辖权的法院裁定为无效、不合法或不可执行，本合同其它条款的有效性、合法性和可执行性不应因此在任何方面受到影响或减损。\n\n# 对本合同的任何修改或变更为无效，除非以书面形式作出并由双方授权代表签字。", "placeholders_in_clause": [], "formatting_guidelines": {"heading_level": 1, "preserve_bold_text": false}, "is_core_clause": false}, {"clause_id": "headings", "clause_title": "标题", "original_content_md": "# 标题\n\n# 本合同中的标题仅为方便查阅而设，不影响本合同任何条款的含义或解释。", "placeholders_in_clause": [], "formatting_guidelines": {"heading_level": 1, "preserve_bold_text": false}, "is_core_clause": false}, {"clause_id": "language_copies_effective_date", "clause_title": "文本、份数与生效", "original_content_md": "# 文本\n\n# 本合同以中文书写，一式【肆】份，甲乙双方各执【贰】份，具有同等法律效力，自双方授权代表签字盖章之日起生效。", "placeholders_in_clause": [{"name": "合同份数-总数", "description": "合同正本的总份数（汉字）", "placeholder_text": "【肆】份", "original_text_example_in_md": "【肆】份"}, {"name": "合同份数-各方持有数", "description": "甲乙双方各执有的合同份数（汉字）", "placeholder_text": "各执【贰】份", "original_text_example_in_md": "各执【贰】份"}], "formatting_guidelines": {"heading_level": 1, "preserve_bold_text": false}, "is_core_clause": false}, {"clause_id": "no_further_text", "clause_title": "结尾", "original_content_md": "(以下无正文)", "placeholders_in_clause": [], "formatting_guidelines": {}, "is_core_clause": false}]}, {"template_name": "标准产品许可合同模板", "description": "用于甲方向乙方（上海脉策数据科技有限公司）采购其标准产品（如Datlas）的许可授权的合同模板。", "keywords": ["产品许可", "<PERSON><PERSON><PERSON>", "软件授权", "上海脉策", "标准产品"], "contract_type": "产品许可合同", "file_name": "【20220607更新】标准产品许可合同模板.md", "core_clauses_identifiers": ["许可产品与服务", "许可期限", "许可费用与支付", "双方的权利与义务", "知识产权", "保密条款", "违约责任"], "fixed_elements": {"contract_number_placeholder_text": "**合同编号：【】**", "party_a_name_placeholder_text": "甲方：【】（\"甲方\"）", "party_a_address_placeholder_text": "联系地址：【】", "party_a_contact_placeholder_text": "联系方式：【】", "party_b_full_name": "乙方：上海脉策数据科技有限公司（\"乙方\"）", "party_b_address": "联系地址：上海市杨浦区政立路421号C座10层", "party_b_contact": "联系方式：021-********", "signing_location_placeholder_text": "签署地：【】", "bank_info_party_b_name": "户名：上海脉策数据科技有限公司", "bank_info_party_b_bank": "开户银行：上海浦东发展银行杨浦支行", "bank_info_party_b_account": "账号：*****************", "invoice_info_party_a_name_placeholder_text": "名称：【】", "invoice_info_party_a_tax_id_placeholder_text": "纳税人识别号：【】", "invoice_info_party_a_address_placeholder_text": "地址：【】", "invoice_info_party_a_phone_placeholder_text": "电话：【】", "invoice_info_party_a_bank_placeholder_text": "开户行：【】", "invoice_info_party_a_account_placeholder_text": "账号：【】", "notice_party_a_address_placeholder_text": "地址：", "notice_party_a_contact_person_placeholder_text": "联系人：", "notice_party_a_phone_placeholder_text": "电话：", "notice_party_a_email_placeholder_text": "邮箱：", "notice_party_b_address": "地址：上海市杨浦区政立路421号10层", "notice_party_b_contact_person_placeholder_text": "联系人：【】", "notice_party_b_phone": "电话：021-********", "notice_party_b_email_placeholder_text": "邮箱：【】"}, "clauses": [{"clause_id": "intro_agreement", "clause_title": "引言", "original_content_md": "双方本着平等自愿、诚实信用的原则，根据《中华人民共和国民法典》及相关法律法规的规定，就甲方向乙方采购标准产品许可及相关技术服务（\"许可产品与服务\"）的相关事宜，经双方友好协商一致，达成如下协议，以资共同遵守。", "placeholders_in_clause": [], "formatting_guidelines": {}, "is_core_clause": false}, {"clause_id": "licensed_products_services", "clause_title": "许可产品与服务", "original_content_md": "## **第一条** **许可产品与服务**\n\n1.1 本合同项下的许可产品与服务具体如下：\n\n（1）许可产品名称：**【Datlas地理大数据平台】**\n\n（2）账号数量：**【】个**\n\n（3）坐席数量：**【】个**\n\n（4）许可产品功能模块：【】\n\n（5）其他服务：【】\n\n（请根据实际采购的许可产品与服务内容填写，可删除或增加表格行数）", "placeholders_in_clause": [{"name": "许可产品名称", "description": "标准产品的具体名称", "placeholder_text": "【Datlas地理大数据平台】", "original_text_example_in_md": "【Datlas地理大数据平台】"}, {"name": "账号数量", "description": "许可的账号数量", "placeholder_text": "【】个", "original_text_example_in_md": "【】个"}, {"name": "坐席数量", "description": "许可的坐席数量", "placeholder_text": "【】个", "original_text_example_in_md": "【】个"}, {"name": "许可产品功能模块", "description": "许可产品包含的具体功能模块列表", "placeholder_text": "【】", "original_text_example_in_md": "【】"}, {"name": "其他服务", "description": "除标准产品外的其他附加服务", "placeholder_text": "【】", "original_text_example_in_md": "【】"}], "formatting_guidelines": {"heading_level": 2, "is_bold_title": true, "numbered_list": true, "preserve_bold_text": true}, "is_core_clause": true}, {"clause_id": "license_term", "clause_title": "许可期限", "original_content_md": "## **第二条** **许可期限**\n\n2.1 许可期限为【】年（\"许可期限\"），自【】年【】月【】日至【】年【】月【】日止。", "placeholders_in_clause": [{"name": "许可年限", "description": "许可的总年限", "placeholder_text": "【】年", "original_text_example_in_md": "【】年"}, {"name": "许可开始日期-年", "description": "许可期限开始年份", "placeholder_text": "【】年", "original_text_example_in_md": "【】年【】月【】日"}, {"name": "许可开始日期-月", "description": "许可期限开始月份", "placeholder_text": "【】月", "original_text_example_in_md": "【】年【】月【】日"}, {"name": "许可开始日期-日", "description": "许可期限开始日", "placeholder_text": "【】日", "original_text_example_in_md": "【】年【】月【】日"}, {"name": "许可结束日期-年", "description": "许可期限结束年份", "placeholder_text": "【】年", "original_text_example_in_md": "【】年【】月【】日"}, {"name": "许可结束日期-月", "description": "许可期限结束月份", "placeholder_text": "【】月", "original_text_example_in_md": "【】年【】月【】日"}, {"name": "许可结束日期-日", "description": "许可期限结束日", "placeholder_text": "【】日", "original_text_example_in_md": "【】年【】月【】日"}], "formatting_guidelines": {"heading_level": 2, "is_bold_title": true, "numbered_list": true}, "is_core_clause": true}, {"clause_id": "license_fee_payment", "clause_title": "许可费用与支付", "original_content_md": "## **第三条** **许可费用与支付**\n\n3.1 本合同项下许可产品与服务的总费用为人民币【】元（大写：人民币【】元整）（\"许可费用\"），此费用包含【6】%的增值税。\n\n3.2 许可费用由甲方在合同签订之日起【】个工作日内一次性支付至乙方指定账户。乙方在收到甲方支付的许可费用后【】个工作日内向甲方开具等额的增值税专用发票。\n\n3.3 乙方银行账户信息：\n\n户名：上海脉策数据科技有限公司\n\n开户银行：上海浦东发展银行杨浦支行\n\n账号：*****************\n\n3.4 甲方开票信息：\n\n名称：【】\n\n纳税人识别号：【】\n\n地址：【】\n\n电话：【】\n\n开户行：【】\n\n账号：【】", "placeholders_in_clause": [{"name": "许可费用总额-人民币数字", "description": "许可费用总金额的人民币小写数字", "placeholder_text": "人民币【】元", "original_text_example_in_md": "人民币【】元"}, {"name": "许可费用总额-人民币大写", "description": "许可费用总金额的人民币大写汉字", "placeholder_text": "（大写：人民币【】元整）", "original_text_example_in_md": "（大写：人民币【】元整）"}, {"name": "增值税税率", "description": "许可费用包含的增值税税率", "placeholder_text": "【6】%", "original_text_example_in_md": "【6】%"}, {"name": "合同签订后付款工作日数", "description": "甲方在合同签订之日起多少个工作日内支付许可费用", "placeholder_text": "【】个工作日", "original_text_example_in_md": "【】个工作日"}, {"name": "收到费用后开票工作日数", "description": "乙方在收到许可费用后多少个工作日内开具发票", "placeholder_text": "【】个工作日", "original_text_example_in_md": "【】个工作日"}, {"name": "甲方发票-公司名称", "description": "甲方开具发票时所需的公司全称", "placeholder_text": "名称：【】", "original_text_example_in_md": "名称：【】"}, {"name": "甲方发票-纳税人识别号", "description": "甲方开具发票时所需的纳税人识别号", "placeholder_text": "纳税人识别号：【】", "original_text_example_in_md": "纳税人识别号：【】"}, {"name": "甲方发票-地址", "description": "甲方开具发票时所需的地址", "placeholder_text": "地址：【】", "original_text_example_in_md": "地址：【】"}, {"name": "甲方发票-电话", "description": "甲方开具发票时所需的电话", "placeholder_text": "电话：【】", "original_text_example_in_md": "电话：【】"}, {"name": "甲方发票-开户行", "description": "甲方开具发票时所需的开户行名称", "placeholder_text": "开户行：【】", "original_text_example_in_md": "开户行：【】"}, {"name": "甲方发票-账号", "description": "甲方开具发票时所需的银行账号", "placeholder_text": "账号：【】", "original_text_example_in_md": "账号：【】"}], "formatting_guidelines": {"heading_level": 2, "is_bold_title": true, "numbered_list": true}, "is_core_clause": true}, {"clause_id": "rights_obligations_parties", "clause_title": "双方的权利与义务", "original_content_md": "## **第四条** **双方的权利与义务**\n\n4.1 甲方的权利与义务\n\n（1）甲方有权在许可期限内，依据本合同约定使用许可产品与服务。\n\n（2）甲方应按照本合同约定及时足额支付许可费用。\n\n（3）甲方不得对许可产品进行反向工程、反编译、反汇编，不得复制、修改、链接、转载、汇编、发表、出版、建立镜像站点等，不得基于许可产品衍生其他产品或服务，或以其他任何方式超出本合同约定的范围使用许可产品。\n\n（4）甲方应自行负责其用户账号和密码的安全，并对通过其账号进行的所有活动和事件承担法律责任。如甲方发现任何非法使用用户账号或存在安全漏洞的情况，应立即通知乙方。\n\n（5）甲方保证其在使用许可产品与服务过程中输入、上传或产生的数据的合法性、合规性，不侵犯任何第三方的合法权益（包括但不限于知识产权、商业秘密、隐私权等）。若因甲方数据导致任何第三方对乙方提出索赔、诉讼或仲裁等，甲方应承担全部责任并赔偿乙方因此遭受的全部损失。\n\n（6）未经乙方书面同意，甲方不得将本合同项下的任何权利或义务转让给任何第三方。\n\n4.2 乙方的权利与义务\n\n（1）乙方应在收到甲方支付的许可费用后【】个工作日内，为甲方开通许可产品与服务的使用权限。\n\n（2）乙方应保证其提供的许可产品与服务符合本合同约定的功能和标准，并提供必要的技术支持和售后服务。\n\n（3）乙方有权根据国家法律法规的变化、许可产品自身的发展及运营需要，对许可产品的功能、界面、服务内容等进行调整、升级或优化，并通过适当方式（如网站公告、邮件通知等）告知甲方。\n\n（4）乙方保证其提供的许可产品与服务不侵犯任何第三方的知识产权或其他合法权益。若因乙方原因导致任何第三方对甲方提出索赔、诉讼或仲裁等，乙方应承担全部责任并赔偿甲方因此遭受的全部损失。\n\n（5）乙方应对甲方的商业秘密和保密信息承担保密义务，具体保密责任见本合同第六条约定。", "placeholders_in_clause": [{"name": "收到费用后开通服务工作日数", "description": "乙方在收到许可费用后多少个工作日内为甲方开通服务", "placeholder_text": "【】个工作日", "original_text_example_in_md": "【】个工作日"}], "formatting_guidelines": {"heading_level": 2, "is_bold_title": true, "numbered_list": true, "preserve_bold_text": true}, "is_core_clause": true}, {"clause_id": "intellectual_property", "clause_title": "知识产权", "original_content_md": "## **第五条** **知识产权**\n\n5.1 本合同项下许可产品（包括但不限于软件、数据、文档、界面设计、商业标识等）的全部知识产权（包括但不限于著作权、专利权、商标权、商业秘密等）均归乙方所有，或许可乙方合法使用。\n\n5.2 甲方仅在本合同约定的许可期限和许可范围内拥有许可产品与服务的非专有、不可转让、不可分许可的使用权。\n\n5.3 甲方通过使用许可产品与服务输入、上传、处理、分析或产生的数据和信息的知识产权归甲方所有，或甲方已获得合法授权。乙方仅为履行本合同之目的接触或使用该等数据，并承诺对该等数据严格保密。\n\n5.4 任何一方不得删除、修改或许可产品上关于知识产权的任何权利声明或标记。", "placeholders_in_clause": [], "formatting_guidelines": {"heading_level": 2, "is_bold_title": true, "numbered_list": true}, "is_core_clause": true}, {"clause_id": "confidentiality", "clause_title": "保密条款", "original_content_md": "## **第六条** **保密条款**\n\n6.1 任何一方（\"接收方\"）对于从另一方（\"披露方\"）获取的、且无法自公开渠道获得的商业秘密、技术信息、经营信息、客户信息等（\"保密信息\"）均负有保密义务。接收方不得将保密信息用于履行本合同之外的任何其他目的，也不得向任何第三方（为履行本合同之目的确有必要知悉该等信息的员工、顾问除外）披露任何保密信息。\n\n6.2 以下信息不属于保密信息：\n\n（1）在披露方披露之时，已为公众所知晓的信息；\n\n（2）在披露方披露之前，接收方已经知晓且没有保密义务的信息；\n\n（3）非因接收方过错而为公众所知晓的信息；\n\n（4）接收方从没有保密义务的第三方合法获得的信息；\n\n（5）接收方独立开发或获得的信息。\n\n6.3 根据法律法规、有管辖权的法院或政府部门的要求，接收方可以披露保密信息，但应在法律允许的范围内及时通知披露方，以便披露方采取适当的保护措施。\n\n6.4 本保密条款在本合同终止后【三】年内持续有效。", "placeholders_in_clause": [{"name": "保密条款有效年限", "description": "合同终止后保密条款的有效年限", "placeholder_text": "【三】年", "original_text_example_in_md": "【三】年"}], "formatting_guidelines": {"heading_level": 2, "is_bold_title": true, "numbered_list": true}, "is_core_clause": true}, {"clause_id": "liability_breach", "clause_title": "违约责任", "original_content_md": "## **第七条** **违约责任**\n\n7.1 任何一方违反本合同的任何约定，均构成违约，应承担相应的违约责任。守约方有权要求违约方继续履行、采取补救措施，或赔偿由此造成的损失（包括但不限于直接经济损失、合理的律师费、诉讼费等）。\n\n7.2 若甲方未按照本合同约定及时足额支付许可费用，每逾期一日，应按逾期支付金额的【0.05】%向乙方支付违约金。逾期超过【30】日的，乙方有权暂停提供许可产品与服务，直至甲方付清全部款项及违约金，或单方解除本合同并要求甲方支付相当于合同总金额【20】%的违约金。\n\n7.3 若因乙方原因导致许可产品与服务无法正常使用，且在甲方书面通知后【15】个工作日内未能修复的，甲方有权要求乙方按照服务中断时间相应减免许可费用，或解除本合同并要求乙方退还已支付但未使用的许可费用。\n\n7.4 在任何情况下，任何一方在本合同项下承担的累计赔偿责任（无论是合同责任、侵权责任或其他责任）均不超过本合同约定的许可费用总额。", "placeholders_in_clause": [{"name": "逾期付款违约金日利率", "description": "甲方逾期付款的每日违约金利率", "placeholder_text": "【0.05】%", "original_text_example_in_md": "【0.05】%"}, {"name": "逾期付款可解除合同天数", "description": "甲方逾期付款超过多少天乙方可解除合同", "placeholder_text": "【30】日", "original_text_example_in_md": "【30】日"}, {"name": "甲方逾期付款解除合同违约金比例", "description": "甲方逾期付款导致乙方解除合同时，甲方应支付的合同总金额百分比作为违约金", "placeholder_text": "【20】%", "original_text_example_in_md": "【20】%"}, {"name": "乙方服务中断可修复工作日数", "description": "乙方服务中断，甲方书面通知后多少个工作日内未能修复，甲方可采取行动", "placeholder_text": "【15】个工作日", "original_text_example_in_md": "【15】个工作日"}], "formatting_guidelines": {"heading_level": 2, "is_bold_title": true, "numbered_list": true}, "is_core_clause": true}, {"clause_id": "force_majeure", "clause_title": "不可抗力", "original_content_md": "## **第八条** **不可抗力**\n\n8.1 \"不可抗力\"是指任何一方无法预见、无法避免且无法克服的客观情况，包括但不限于自然灾害（如地震、台风、洪水、火灾）、战争、动乱、罢工、政府行为、法律法规变化、网络中断、电力中断等。\n\n8.2 若任何一方因不可抗力事件导致无法履行或延迟履行本合同项下任何义务，不应视为违约。受影响的一方应在不可抗力事件发生后【7】日内书面通知另一方，并提供相关证明文件。双方应根据不可抗力事件对本合同履行的影响程度，协商决定是否延迟履行、部分履行或终止履行本合同。\n\n8.3 若不可抗力事件持续超过【60】日，任何一方均有权书面通知另一方终止本合同，双方互不承担违约责任。", "placeholders_in_clause": [{"name": "不可抗力通知期限", "description": "不可抗力事件发生后多少日内需书面通知对方", "placeholder_text": "【7】日", "original_text_example_in_md": "【7】日"}, {"name": "不可抗力持续可解除合同天数", "description": "不可抗力事件持续超过多少天任何一方可终止合同", "placeholder_text": "【60】日", "original_text_example_in_md": "【60】日"}], "formatting_guidelines": {"heading_level": 2, "is_bold_title": true, "numbered_list": true}, "is_core_clause": false}, {"clause_id": "law_dispute_resolution", "clause_title": "法律适用与争议解决", "original_content_md": "## **第九条** **法律适用与争议解决**\n\n9.1 本合同的订立、效力、解释、履行及争议解决均适用中华人民共和国法律（为本合同之目的，不包括香港、澳门及台湾地区法律）。\n\n9.2 因本合同引起的或与本合同有关的任何争议，双方应首先通过友好协商解决。协商不成的，任何一方均有权向【被告所在地】有管辖权的人民法院提起诉讼。", "placeholders_in_clause": [{"name": "争议解决法院管辖地", "description": "发生争议时选择的管辖法院所在地，例如：被告所在地、合同签订地等", "placeholder_text": "【被告所在地】", "original_text_example_in_md": "【被告所在地】"}], "formatting_guidelines": {"heading_level": 2, "is_bold_title": true, "numbered_list": true}, "is_core_clause": false}, {"clause_id": "notices", "clause_title": "通知与送达", "original_content_md": "## **第十条** **通知与送达**\n\n10.1 本合同项下的任何通知、请求、同意或其他通讯均应以书面形式作出，并通过专人递送、挂号信、传真或电子邮件等方式发送至本合同首页载明的双方联系地址或邮箱。若以专人递送，则在送达时视为收到；若以挂号信发送，则在寄出后第【5】日视为收到；若以传真发送，则在成功发送并收到确认回执时视为收到；若以电子邮件发送，则在邮件进入对方指定邮箱系统时视为收到。\n\n10.2 任何一方变更联系地址、邮箱或其他联系方式的，应至少提前【3】个工作日书面通知另一方。否则，向原地址或邮箱发送的通知仍视为有效送达。\n\n**甲方指定联系方式：**\n\n地址：\n\n联系人：\n\n电话：\n\n邮箱：\n\n**乙方指定联系方式：**\n\n地址：上海市杨浦区政立路421号10层\n\n联系人：【】\n\n电话：021-********\n\n邮箱：【】", "placeholders_in_clause": [{"name": "挂号信送达天数", "description": "以挂号信方式发送通知，寄出后多少日视为收到", "placeholder_text": "第【5】日", "original_text_example_in_md": "第【5】日"}, {"name": "变更联系方式提前通知工作日数", "description": "变更联系方式应至少提前多少个工作日书面通知对方", "placeholder_text": "【3】个工作日", "original_text_example_in_md": "【3】个工作日"}, {"name": "甲方通知地址", "description": "甲方接收通知的地址", "placeholder_text": "地址：", "original_text_example_in_md": "地址："}, {"name": "甲方通知联系人", "description": "甲方接收通知的联系人", "placeholder_text": "联系人：", "original_text_example_in_md": "联系人："}, {"name": "甲方通知电话", "description": "甲方接收通知的电话", "placeholder_text": "电话：", "original_text_example_in_md": "电话："}, {"name": "甲方通知邮箱", "description": "甲方接收通知的邮箱", "placeholder_text": "邮箱：", "original_text_example_in_md": "邮箱："}, {"name": "乙方通知联系人", "description": "乙方接收通知的联系人", "placeholder_text": "联系人：【】", "original_text_example_in_md": "联系人：【】"}, {"name": "乙方通知邮箱", "description": "乙方接收通知的邮箱", "placeholder_text": "邮箱：【】", "original_text_example_in_md": "邮箱：【】"}], "formatting_guidelines": {"heading_level": 2, "is_bold_title": true, "numbered_list": true, "preserve_bold_text": true}, "is_core_clause": false}, {"clause_id": "miscellaneous", "clause_title": "其他", "original_content_md": "## **第十一条** **其他**\n\n11.1 本合同自双方授权代表签字并加盖公司公章（或合同专用章）之日起生效。\n\n11.2 本合同未尽事宜，双方可另行签订补充协议。补充协议与本合同具有同等法律效力。\n\n11.3 本合同的标题仅为方便阅读而设，不影响本合同任何条款的含义或解释。\n\n11.4 本合同任何条款的无效或不可执行不影响其他条款的效力。\n\n11.5 本合同一式【肆】份，甲乙双方各执【贰】份，具有同等法律效力。", "placeholders_in_clause": [{"name": "合同份数-总数", "description": "合同正本的总份数（汉字）", "placeholder_text": "【肆】份", "original_text_example_in_md": "【肆】份"}, {"name": "合同份数-各方持有数", "description": "甲乙双方各执有的合同份数（汉字）", "placeholder_text": "各执【贰】份", "original_text_example_in_md": "各执【贰】份"}], "formatting_guidelines": {"heading_level": 2, "is_bold_title": true, "numbered_list": true}, "is_core_clause": false}, {"clause_id": "signatures", "clause_title": "签署部分", "original_content_md": "（以下无正文）\n\n**甲方（盖章）：**\n\n授权代表（签字）：\n\n日期： 年 月 日\n\n**乙方（盖章）：上海脉策数据科技有限公司**\n\n授权代表（签字）：\n\n日期： 年 月 日", "placeholders_in_clause": [], "formatting_guidelines": {"preserve_spacing_and_layout": true, "preserve_bold_text": true}, "is_core_clause": false}]}]