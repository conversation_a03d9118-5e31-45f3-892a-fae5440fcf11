{"template_name": "全定制技术服务合同模板", "description": "适用于为客户特定项目提供全面定制化技术服务的场景，涵盖服务内容、费用、交付验收、双方权利义务、知识产权、保密等核心条款。", "keywords": ["技术服务", "定制开发", "项目合同", "软件开发", "系统集成"], "contract_type": "技术服务合同", "file_name_prefix": "custom_technical_service_contract", "llm_group_id_order": ["metadata_block", "preamble_block", "main_content_block", "payment_block", "standard_clauses_block", "signature_block"], "template_version": "20210524", "template_note": "全定制服务合同模板（不涉及datlas）", "metadata_block": {"llm_group_id": "metadata_block", "contract_title": "技术服务合同", "contract_number_placeholder": "合同编号：[__CONTRACT_NUMBER__]", "sign_date_placeholder": "签订时间：[__SIGN_YEAR__]年[__SIGN_MONTH__]月", "party_a_name_placeholder": "甲方：[__PARTY_A_NAME__]", "party_a_address_placeholder": "联系地址：[__PARTY_A_ADDRESS__]", "party_a_contact_placeholder": "联系方式：[__PARTY_A_CONTACT__]", "party_b_name_fixed": "乙方：上海脉策数据科技有限公司", "party_b_address_fixed": "联系地址：上海市杨浦区政立路421号C座10层", "party_b_contact_fixed": "联系方式：021-61070586", "relationship_definition": "甲方和乙方在本合同中单独称为“一方”，合称“双方”。"}, "preamble_block": {"llm_group_id": "preamble_block", "text_content": "双方根据《中华人民共和国民法典》及相关法律法规的规定，本着平等自愿的原则，经双方友好协商一致，就甲方委托乙方针对[__PROJECT_NAME__]平台项目(以下简称“本项目”)提供技术服务(“服务”)的相关事宜，达成如下合意，以兹共同遵守。"}, "sections_block": {"sections": [{"section_number": "1", "section_title": "技术服务内容与服务时间", "llm_group_id": "main_content_block", "subsections": [{"subsection_number": "1.1", "subsection_title": "服务内容", "content_intro_text": "本合同提供服务包括以下内容：", "format": "table", "table_content": [["功能名称", "功能描述"], ["[__FUNCTION_1_NAME__]", "[__FUNCTION_1_DESCRIPTION__]"], ["[__FUNCTION_2_NAME__]", "[__FUNCTION_2_DESCRIPTION__]"], ["[__FUNCTION_3_NAME__]", "[__FUNCTION_3_DESCRIPTION__]"], ["[__FUNCTION_4_NAME__]", "[__FUNCTION_4_DESCRIPTION__]"]]}, {"subsection_number": "1.2", "subsection_title": "运维服务期", "content": "运维服务期为[__MAINTENANCE_SERVICE_PERIOD__] (例如：一年(365天))，自开发成果验收合格之日起计算。", "format": "text"}]}, {"section_number": "2", "section_title": "技术服务费用及支付方式", "llm_group_id": "payment_block", "subsections": [{"subsection_number": "2.1", "subsection_title": "费用", "content_main": "作为乙方向甲方提供本合同项下提供服务的对价，甲方应向乙方支付技术服务费总额人民币[__TOTAL_SERVICE_FEE_AMOUNT__]元(大写：[__TOTAL_SERVICE_FEE_AMOUNT_IN_WORDS__]万元整)(含6%的增值税，\"技术服务费\")。具体而言，本技术服务费包含以下部分：", "format": "table", "table_content": [["服务", "价格"], ["[__SERVICE_ITEM_1_NAME__]", "[__SERVICE_ITEM_1_PRICE__]"], ["[__SERVICE_ITEM_2_NAME__]", "[__SERVICE_ITEM_2_PRICE__]"], ["[__SERVICE_ITEM_3_NAME__]", "[__SERVICE_ITEM_3_PRICE__]"], ["[__SERVICE_ITEM_4_NAME__]", "[__SERVICE_ITEM_4_PRICE__]"]]}, {"subsection_number": "2.2", "subsection_title": "支付方式", "content_intro_text": "技术服务费由甲方分[__PAYMENT_INSTALLMENTS_NUMBER__]期支付给乙方，具体支付时间和金额约定如下：", "format": "table", "table_content": [["序号", "里程碑事件", "支付金额"], ["第一期", "[__MILESTONE_1_EVENT__] (例如：本合同签订后)", "技术服务费的30%\n即人民币[__PAYMENT_1_AMOUNT__]元(大写：[__PAYMENT_1_AMOUNT_IN_WORDS__]万元整)"], ["第二期", "[__MILESTONE_2_EVENT__] (例如：平台系统上线后)", "技术服务费的60%\n即人民币[__PAYMENT_2_AMOUNT__]元(大写：[__PAYMENT_2_AMOUNT_IN_WORDS__]万元整)"], ["第三期", "[__MILESTONE_3_EVENT__] (例如：经甲方验收合格后)", "技术服务费的10%\n即人民币[__PAYMENT_3_AMOUNT__]元(大写：[__PAYMENT_3_AMOUNT_IN_WORDS__]万元整)"]], "additional_content": "在每一期付款的里程碑事件达成后，乙方向甲方开具合法有效的、与实际支付金额一致的增值税专用发票。甲方在收到发票后[__PAYMENT_DAYS_AFTER_INVOICE__]个工作日内向乙方指定的账户全额支付当期的技术服务费用。甲方理解并同意，在乙方收到本合同约定的每一期全额技术服务费之前，乙方有权拒绝继续提供服务、提供下一阶段的服务或交付下一阶段的交付成果。"}, {"subsection_number": "2.3", "subsection_title": "支出", "content": "除双方另有规定外，前述服务费仅为乙方向甲方提供本合同项下交付成果的对价，不含乙方由于受委托提供服务所产生的如差旅费、住宿费等各项费用(\"支出\")。乙方的支出应向甲方申请核准后由甲方在服务费外另行实报实销。", "format": "text"}, {"subsection_number": "2.4", "subsection_title": "乙方开户信息", "content_intro_text": "乙方开户银行名称、地址和账号为：", "format": "list", "list_content": ["户名：上海脉策数据科技有限公司", "开户银行：上海浦东发展银行杨浦支行", "账号：9812 0154 7400 1146 3", "纳税人名称：上海脉策数据科技有限公司", "纳税人识别号：91310110342309083H"]}, {"subsection_number": "2.5", "subsection_title": "甲方开票信息", "content_intro_text": "甲方开票信息：", "format": "list", "list_content": ["名称：[__PARTY_A_INVOICE_NAME__]", "纳税人识别号：[__PARTY_A_TAX_ID__]", "地址：[__PARTY_A_INVOICE_ADDRESS__]", "电话：[__PARTY_A_INVOICE_PHONE__]", "开户行：[__PARTY_A_INVOICE_BANK__]", "账号：[__PARTY_A_INVOICE_ACCOUNT__]"]}]}, {"section_number": "3", "section_title": "成果交付及验收", "llm_group_id": "main_content_block", "subsections": [{"subsection_number": "3.1", "subsection_title": "成果交付", "content": "甲方项目联系人应在乙方完成交付后签署相关确认文件。", "format": "text"}, {"subsection_number": "3.2", "subsection_title": "验收范围", "content": "本项目采用[__ACCEPTANCE_METHOD__] (例如：现场验收)方式验收，以本合同第1条约定的服务为验收范围。", "format": "text"}, {"subsection_number": "3.3", "subsection_title": "验收条件", "content": "[__ACCEPTANCE_CRITERIA__] (备注：具体验收条件由项目负责人根据项目具体情况确定)", "format": "text"}, {"subsection_number": "3.4", "subsection_title": "验收时间", "content": "成果交付之日起[__ACCEPTANCE_PERIOD_MONTHS__] (例如：3)个月内(\"验收期\")，甲方应组织验收。验收合格确认单可由本合同约定的甲方项目联系人签字后，向乙方提供确认单原件或扫描件。", "format": "text"}, {"subsection_number": "3.5", "subsection_title": "验收流程", "content": "甲方认为系统存在问题需要修复的，应当在验收期内通过书面形式向乙方反馈。甲方未在验收期内进行验收、未向乙方提出书面修复要求的，视为乙方交付的成果符合本合同约定，验收合格。合同约定的服务期内，乙方对平台系统承担维护义务，保证平台系统正常使用。", "format": "text"}, {"subsection_number": "3.6", "subsection_title": "免责情形", "content_intro_text": "无论其他任何约定，以下情形不属于乙方原因导致的瑕疵或缺陷，乙方无需承担任何违约责任，亦无义务免费提供任何补救或维护服务：", "format": "list", "list_content": ["甲方未按照乙方提供的方法、规则、流程等进行操作，从而引起的平台系统的任何瑕疵或缺陷；", "甲方未能满足平台系统所需环境条件或外部要求，从而引起的平台系统的任何瑕疵或缺陷；", "甲方未经乙方同意，对平台系统进行了拆除、变更、重装、改装等行为，从而引起的平台系统的任何瑕疵或缺陷；", "甲方将平台系统用于本合同之外目的，从而引起的平台系统的任何瑕疵或缺陷。"]}]}, {"section_number": "4", "section_title": "双方权利义务", "llm_group_id": "main_content_block", "subsections": [{"subsection_number": "4.1", "subsection_title": "甲方的权利义务", "format": "list", "list_content": ["甲方应按照合同约定及时足额地向乙方支付服务费用。", "甲方负责提供业务需求资料。", "甲方须及时配合乙方对系统进行测试和试运行，并及时反馈修改意见给乙方。乙方可根据甲方的反馈，配置专项人员，提供必要的技术服务和培训支持，乙方对甲方提出的技术服务相关问题提供的答疑服务响应时间不超过24小时。", "甲方保证向乙方提供的或在使用乙方产品过程中使用的任何数据或信息均不以任何方式或途径侵犯任何第三方的合法权益(包括但不限于知识产权、隐私权等)，亦不会造成任何第三方向乙方追索任何赔偿或补偿等，否则由甲方承担相应责任，并赔偿由此给乙方造成的损失。", "乙方服务仅供甲方在中国大陆范围内使用，甲方不得违反法律规定向境外（包含中国香港、澳门、台湾）传输从乙方获取的数据。", "若因甲方未能履行本条款约定的义务导致乙方提供服务延后的，乙方的交付期限应相应延长，且不视为乙方违约。"]}, {"subsection_number": "4.2", "subsection_title": "乙方的权利义务", "format": "list", "list_content": ["乙方负责根据甲方的具体需求进行设计开发，并及时与甲方沟通，确保系统的功能符合实际操作和管理需要。", "乙方应在收到甲方第一期付款后[__DEVELOPMENT_PLAN_DAYS_AFTER_PAYMENT__]个工作日内完成项目的调研、开发、上线计划。", "乙方负责根据甲方需求进行开发，提供高质量的运行系统。", "运维服务内，乙方应确保系统性能稳定和运行正常，由于乙方技术问题带来的服务中断，乙方负责尽快恢复服务。"]}]}, {"section_number": "5", "section_title": "项目联系人职责", "llm_group_id": "main_content_block", "subsections": [{"subsection_number": "5.1", "subsection_title": "项目联系人指定", "content": "双方约定在本合同有效期内，甲方指定[__PARTY_A_CONTACT_PERSON__]为甲方项目联系人，乙方指定[__PARTY_B_CONTACT_PERSON__]为乙方项目联系人。项目联系人的联系方式以本合同第[__CONTACT_INFO_CLAUSE_NUMBER__] (例如：13)条为准。", "format": "text"}, {"subsection_number": "5.2", "subsection_title": "联系人职责", "content_intro_text": "项目联系人在本合同履行过程中承担以下职责：", "format": "list", "list_content": ["沟通协调，保障项目顺利开展；", "项目交付验收及相关确认文件的签署；", "甲方付款的关联公司变更情况通知与确认。"]}, {"subsection_number": "5.3", "subsection_title": "联系人变更", "content": "一方变更项目联系人的，应当及时以书面形式通知另一方。未及时通知并影响本合同履行或造成损失的，应承担相应的责任。", "format": "text"}]}, {"section_number": "6", "section_title": "陈述与保证", "llm_group_id": "standard_clauses_block", "content": "[__REPRESENTATIONS_AND_WARRANTIES_CONTENT__] (此部分内容通常比较固定，可参考标准模板或法务意见)"}, {"section_number": "7", "section_title": "保密条款", "llm_group_id": "standard_clauses_block", "content": "[__CONFIDENTIALITY_AGREEMENT_CONTENT__] (参考标准保密条款，明确保密信息范围、保密期限、例外情况等)"}, {"section_number": "8", "section_title": "知识产权", "llm_group_id": "standard_clauses_block", "content": "[__INTELLECTUAL_PROPERTY_RIGHTS_CONTENT__] (明确服务过程中产生的知识产权归属、已有知识产权的使用授权等)"}, {"section_number": "9", "section_title": "违约责任", "llm_group_id": "standard_clauses_block", "content": "[__LIABILITY_FOR_BREACH_OF_CONTRACT_CONTENT__] (约定各方违约的具体情形、违约金计算方式、赔偿范围等)"}, {"section_number": "10", "section_title": "不可抗力", "llm_group_id": "standard_clauses_block", "content": "[__FORCE_MAJEURE_CONTENT__] (参照标准不可抗力条款，明确构成不可抗力的事件、通知义务、责任免除等)"}, {"section_number": "11", "section_title": "合同解除", "llm_group_id": "standard_clauses_block", "content": "[__CONTRACT_TERMINATION_CONDITIONS_CONTENT__] (约定合同可以单方或双方协商解除的具体条件和程序)"}, {"section_number": "12", "section_title": "争议解决", "llm_group_id": "standard_clauses_block", "content": "[__DISPUTE_RESOLUTION_MECHANISM_CONTENT__] (约定争议解决方式，如协商、调解、仲裁、诉讼，并明确管辖机构或法院)"}, {"section_number": "13", "section_title": "通知与送达", "llm_group_id": "standard_clauses_block", "content": "[__NOTICE_AND_SERVICE_CONTENT__] (明确双方正式通知的送达方式、地址、邮箱等，以及视为送达的时间)"}, {"section_number": "14", "section_title": "其他约定", "llm_group_id": "standard_clauses_block", "content": "[__OTHER_AGREEMENTS_CONTENT__] (可包括合同的完整性、可分割性、标题效力、文本份数等其他双方一致同意的事项)"}]}, "signature_block": {"llm_group_id": "signature_block", "party_a_signature_area": "甲方(盖章)：\n法定代表人或受委托人(签字)：\n签约时间：[__PARTY_A_SIGN_DATE__]年[__PARTY_A_SIGN_MONTH__]月[__PARTY_A_SIGN_DAY__]日", "party_b_signature_area": "乙方(盖章)：上海脉策数据科技有限公司\n法定代表人或受委托人(签字)：\n签约时间：[__PARTY_B_SIGN_DATE__]年[__PARTY_B_SIGN_MONTH__]月[__PARTY_B_SIGN_DAY__]日"}}