// 合同生成相关类型定义

// 生成状态枚举
export enum GenerationStatus {
  IDLE = 'idle',
  GENERATING = 'generating',
  SUCCESS = 'success',
  ERROR = 'error',
}

// 合同生成结果接口
export interface ContractGenerationResult {
  contract_markdown: string;
  selected_template_name: string;
}

// 合同模板信息接口
export interface ContractTemplate {
  template_filename: string;
  contract_type?: string;
  template_note?: string;
  contract_title?: string;
  [key: string]: any;
}

// 生成日志接口
export interface GenerationLog {
  timestamp: Date;
  message: string;
  level: 'info' | 'warning' | 'error';
}
