import { type FC, memo } from 'react';

import { useController } from '@/shared/hooks/useController';

import { ContractInputForm } from './components/ContractInputForm';
import { ContractPreview } from './components/ContractPreview';
import { GenerationLogs } from './components/GenerationLogs';
import { HomeController } from './HomeController';

// 页面内容
const PageMain: FC<{ controller: HomeController }> = memo(({ controller }) => {
  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold text-gray-900">智能合同生成助手</h1>
        <p className="text-gray-600">基于AI技术，快速生成专业合同文档</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 左侧：输入表单 */}
        <div className="space-y-4">
          <ContractInputForm controller={controller} />
          <GenerationLogs controller={controller} />
        </div>

        {/* 右侧：预览区域 */}
        <div>
          <ContractPreview controller={controller} />
        </div>
      </div>
    </div>
  );
});

// 主页面组件
const HomePage: FC = () => {
  const [controller] = useController(() => {
    const controller = new HomeController();
    return [controller, {}];
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      <PageMain controller={controller} />
    </div>
  );
};

export default memo(HomePage);
