import { type FC, memo } from 'react';

import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { useObservableState } from '@/shared/hooks/useObservableState';

import { type HomeController } from '../HomeController';

interface GenerationLogsProps {
  controller: HomeController;
}

export const GenerationLogs: FC<GenerationLogsProps> = memo(({ controller }) => {
  const logs = useObservableState(controller.generationLogs$, []);

  if (logs.length === 0) {
    return null;
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-sm">生成日志</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="max-h-32 overflow-y-auto space-y-1">
          {logs.map((log, index) => (
            <div
              key={index}
              className="text-xs text-gray-600 font-mono bg-gray-50 px-2 py-1 rounded"
            >
              {log}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
});

GenerationLogs.displayName = 'GenerationLogs';
