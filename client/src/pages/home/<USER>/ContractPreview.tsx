import { type FC, memo } from 'react';

import { MarkdownRender } from '@/components/MarkdownRender';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useObservableState } from '@/shared/hooks/useObservableState';

import { GenerationStatus, type HomeController } from '../HomeController';

interface ContractPreviewProps {
  controller: HomeController;
}

export const ContractPreview: FC<ContractPreviewProps> = memo(({ controller }) => {
  const contractResult = useObservableState(controller.contractResult$, null);
  const generationStatus = useObservableState(controller.generationStatus$, GenerationStatus.IDLE);

  const isGenerating = generationStatus === GenerationStatus.GENERATING;
  const hasResult = contractResult !== null;

  const handleDownload = () => {
    controller.downloadContract();
  };

  if (isGenerating) {
    return (
      <Card className="size-full">
        <CardHeader>
          <CardTitle>合同预览</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64">
            <div className="text-center space-y-2">
              <div className="animate-spin rounded-full size-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-sm text-gray-600">正在生成合同，请稍候...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!hasResult) {
    return (
      <Card className="size-full">
        <CardHeader>
          <CardTitle>合同预览</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64">
            <p className="text-gray-500">请输入合同需求并点击生成合同</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="size-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle>合同预览</CardTitle>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={handleDownload}>
            下载 Markdown
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="max-h-[600px] overflow-y-auto border rounded-md p-4 bg-white">
          <MarkdownRender className="prose prose-sm max-w-none">
            {contractResult.contract_markdown}
          </MarkdownRender>
        </div>
        <div className="mt-4 text-sm text-gray-600">
          <p>模板: {contractResult.selected_template_name}</p>
        </div>
      </CardContent>
    </Card>
  );
});

ContractPreview.displayName = 'ContractPreview';
